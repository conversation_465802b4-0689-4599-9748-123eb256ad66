package com.dcjet.cs.api.auxiliaryMaterials;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractListParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractListExportParam;
import com.dcjet.cs.auxiliaryMaterials.service.BizIAuxmatBuyContractListService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-29
 */
@RestController
@RequestMapping("v1/bizIAuxmatBuyContractList")
@Api(tags = "接口")
public class BizIAuxmatBuyContractListController extends BaseController {
    @Resource
    private BizIAuxmatBuyContractListService bizIAuxmatBuyContractListService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIAuxmatBuyContractListParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIAuxmatBuyContractListDto>> getListPaged(@RequestBody BizIAuxmatBuyContractListParam bizIAuxmatBuyContractListParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIAuxmatBuyContractListDto>> paged = bizIAuxmatBuyContractListService.getListPaged(bizIAuxmatBuyContractListParam, pageParam);
        return paged;
    }
    /**
     * @param bizIAuxmatBuyContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIAuxmatBuyContractListDto> insert(@Valid @RequestBody BizIAuxmatBuyContractListParam bizIAuxmatBuyContractListParam, UserInfoToken userInfo) {
        ResultObject<BizIAuxmatBuyContractListDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIAuxmatBuyContractListDto bizIAuxmatBuyContractListDto = bizIAuxmatBuyContractListService.insert(bizIAuxmatBuyContractListParam, userInfo);
        if (bizIAuxmatBuyContractListDto != null) {
            resultObject.setData(bizIAuxmatBuyContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIAuxmatBuyContractListParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIAuxmatBuyContractListDto> update(@PathVariable String sid, @Valid @RequestBody BizIAuxmatBuyContractListParam bizIAuxmatBuyContractListParam, UserInfoToken userInfo) {
        bizIAuxmatBuyContractListParam.setSid(sid);
        BizIAuxmatBuyContractListDto bizIAuxmatBuyContractListDto = bizIAuxmatBuyContractListService.update(bizIAuxmatBuyContractListParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIAuxmatBuyContractListDto != null) {
            resultObject.setData(bizIAuxmatBuyContractListDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIAuxmatBuyContractListService.delete(sids, userInfo);
        return resultObject;
    }
}
