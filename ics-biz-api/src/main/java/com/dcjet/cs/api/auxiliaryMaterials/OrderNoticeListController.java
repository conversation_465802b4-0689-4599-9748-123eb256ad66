package com.dcjet.cs.api.auxiliaryMaterials;

import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeListParam;
import com.dcjet.cs.auxiliaryMaterials.service.OrderNoticeListService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/importAuxMat/orderNoticeList")
@Api(tags = "国营贸易进口辅料-订货通知表体")
public class OrderNoticeListController extends BaseController {
    @Resource
    private OrderNoticeListService orderNoticeListService;

    /**
     * 获取分页列表
     *
     * @param orderNoticeListParam 订货通知表体参数
     * @param pageParam            分页参数
     * @param userInfo             用户信息
     * @return 订货通知表体分页列表
     */
    @ApiOperation("获取订货通知表体分页列表")
    @PostMapping("/list")
    public ResultObject<List<OrderNoticeListDto>> getListPaged(@RequestBody OrderNoticeListParam orderNoticeListParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.orderNoticeListService.getListPaged(orderNoticeListParam, pageParam, userInfo);
    }

    /**
     * 修改订货通知表体
     *
     * @param sid                  主键
     * @param orderNoticeListParam 订货通知表体参数
     * @param userInfo             用户信息
     * @return 订货通知传输模型
     */
    @ApiOperation("修改订货通知表体")
    @PutMapping("/{sid}")
    public ResultObject<?> update(@PathVariable String sid, @RequestBody @Valid OrderNoticeListParam orderNoticeListParam
            , UserInfoToken<?> userInfo) {
        orderNoticeListParam.setSid(sid);
        OrderNoticeListDto orderNoticeListDto = this.orderNoticeListService.update(orderNoticeListParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, orderNoticeListDto);
    }

    /**
     * 删除订货通知表体
     *
     * @param sids     主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除订货通知表体")
    @DeleteMapping("/{sids}")
    public ResultObject<?> delete(@PathVariable List<String> sids, UserInfoToken<?> userInfo) {
        this.orderNoticeListService.deleteBySids(sids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }
}