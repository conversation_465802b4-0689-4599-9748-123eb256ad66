package com.dcjet.cs.api.auxiliaryMaterials;

import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.dto.base.BasicExportParam;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeHeadDto;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeHeadParam;
import com.dcjet.cs.auxiliaryMaterials.service.OrderNoticeHeadService;
import com.dcjet.cs.util.variable.CommonVariable;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/v1/importAuxMat/orderNoticeHead")
@Api(tags = "国营贸易进口辅料-订货通知表头")
public class OrderNoticeHeadController extends BaseController {
    @Resource
    private OrderNoticeHeadService orderNoticeHeadService;
    @Resource
    private ExcelService excelService;

    /**
     * 获取分页列表
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param pageParam            分页参数
     * @param userInfo             用户信息
     * @return 订货通知表头分页列表
     */
    @ApiOperation("获取订货通知表头分页列表")
    @PostMapping("/list")
    public ResultObject<List<OrderNoticeHeadDto>> getListPaged(@RequestBody OrderNoticeHeadParam orderNoticeHeadParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        return this.orderNoticeHeadService.getListPaged(orderNoticeHeadParam, pageParam, userInfo);
    }


    /**
     * 新增订货通知表头
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 订货通知表头传输模型
     */
    @ApiOperation("新增订货通知表头")
    @PostMapping
    public ResultObject<?> insert(@Valid @RequestBody OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        OrderNoticeHeadDto orderNoticeHeadDto = this.orderNoticeHeadService.insert(orderNoticeHeadParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS, orderNoticeHeadDto);
    }

    /**
     * 修改订货通知表头
     *
     * @param sid                  主键
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 订货通知表头传输模型
     */
    @ApiOperation("修改订货通知表头")
    @PutMapping("/{sid}")
    public ResultObject<?> update(@PathVariable String sid, @Valid @RequestBody OrderNoticeHeadParam orderNoticeHeadParam
            , UserInfoToken<?> userInfo) {
        orderNoticeHeadParam.setSid(sid);
        OrderNoticeHeadDto orderNoticeHeadDto = this.orderNoticeHeadService.update(orderNoticeHeadParam, userInfo);
        return ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS, orderNoticeHeadDto);
    }

    /**
     * 删除订货通知表头
     *
     * @param sids     主键列表
     * @param userInfo 用户信息
     * @return 结果对象
     */
    @ApiOperation("删除订货通知表头")
    @DeleteMapping("/{sids}")
    public ResultObject<?> delete(@PathVariable List<String> sids, UserInfoToken<?> userInfo) {
        this.orderNoticeHeadService.deleteBySids(sids, userInfo);
        return ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
    }

    /**
     * 导出订货通知表头列表
     *
     * @param exportParam 导出参数
     * @param userInfo    用户信息
     * @return 响应实体
     */
    @ApiOperation("导出订货通知表头列表")
    @PostMapping("/export")
    public ResponseEntity<?> export(@RequestBody BasicExportParam<OrderNoticeHeadParam> exportParam, UserInfoToken<?> userInfo) {
        List<OrderNoticeHeadDto> dtoList = this.orderNoticeHeadService.getAllList(exportParam.getExportColumns(), userInfo);
        try {
            return this.excelService.getExcelHeaders(URLEncoder.encode(exportParam.getName(), CommonVariable.UTF8)
                    , exportParam.getHeader(), dtoList);
        } catch (Exception e) {
            throw new ErrorException(500, "导出失败!");
        }
    }
}