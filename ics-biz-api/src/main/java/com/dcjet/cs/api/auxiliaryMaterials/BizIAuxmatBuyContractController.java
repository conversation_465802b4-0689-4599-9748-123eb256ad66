package com.dcjet.cs.api.auxiliaryMaterials;
import com.dcjet.cs.common.service.ExcelService;
import com.dcjet.cs.common.model.PCodeType;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractDto;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractParam;
import com.dcjet.cs.dto.auxiliaryMaterials.BizIAuxmatBuyContractExportParam;
import com.dcjet.cs.auxiliaryMaterials.service.BizIAuxmatBuyContractService;
import com.xdo.pcode.service.PCodeHolder;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import com.xdo.common.token.UserInfoToken;
import com.xdo.springboot.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.List;
/**
 * generated by Generate dc
 * 
 *
 * <AUTHOR>
 * @date: 2025-5-28
 */
@RestController
@RequestMapping("v1/bizIAuxmatBuyContract")
@Api(tags = "接口")
public class BizIAuxmatBuyContractController extends BaseController {
    @Resource
    private BizIAuxmatBuyContractService bizIAuxmatBuyContractService;
    @Resource
    private PCodeHolder pCodeHolder;
    @Resource
    private ExcelService excelService;
    /**
     * @param bizIAuxmatBuyContractParam
     * @param pageParam
     * @param userInfo
     * @return
     */
    @ApiOperation("分页查询接口")
    @PostMapping("list")
    public ResultObject<List<BizIAuxmatBuyContractDto>> getListPaged(@RequestBody BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, PageParam pageParam, UserInfoToken userInfo) {
        ResultObject<List<BizIAuxmatBuyContractDto>> paged = bizIAuxmatBuyContractService.getListPaged(bizIAuxmatBuyContractParam, pageParam);
        return paged;
    }
    /**
     * @param bizIAuxmatBuyContractParam
     * @param userInfo
     * @return
     */
    @ApiOperation("新增接口")
    @PostMapping()
    public ResultObject<BizIAuxmatBuyContractDto> insert(@Valid @RequestBody BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, UserInfoToken userInfo) {
        ResultObject<BizIAuxmatBuyContractDto> resultObject = ResultObject.createInstance(true, ResultObject.INSERT_SUCCESS);
        BizIAuxmatBuyContractDto bizIAuxmatBuyContractDto = bizIAuxmatBuyContractService.insert(bizIAuxmatBuyContractParam, userInfo);
        if (bizIAuxmatBuyContractDto != null) {
            resultObject.setData(bizIAuxmatBuyContractDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * @param sid
     * @param bizIAuxmatBuyContractParam
     * @param userInfo
     * @return
     */
    @ApiOperation("修改接口")
    @PutMapping("{sid}")
    public ResultObject<BizIAuxmatBuyContractDto> update(@PathVariable String sid, @Valid @RequestBody BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, UserInfoToken userInfo) {
        bizIAuxmatBuyContractParam.setSid(sid);
        BizIAuxmatBuyContractDto bizIAuxmatBuyContractDto = bizIAuxmatBuyContractService.update(bizIAuxmatBuyContractParam, userInfo);
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.UPDATE_SUCCESS);
        if (bizIAuxmatBuyContractDto != null) {
            resultObject.setData(bizIAuxmatBuyContractDto);
        } else {
            resultObject.setSuccess(false);
            resultObject.setMessage(ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }
    // TODO patch api
    /**
     * @param sids
     * @return
     */
    @ApiOperation("删除接口")
    @DeleteMapping("{sids}")
    public ResultObject delete(@PathVariable List<String> sids, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, ResultObject.DELETE_SUCCESS);
        // 检查是否存在表体数据
		 bizIAuxmatBuyContractService.delete(sids, userInfo);
        return resultObject;
    }
}
