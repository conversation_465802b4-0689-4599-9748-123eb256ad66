package com.dcjet.cs.dto.auxiliaryMaterials;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-5-29
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizIAuxmatBuyContractListDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String id;
	/**
      * 关联SID
      */
    @ApiModelProperty("关联SID")
	private  String headId;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	/**
      * 产品型号
      */
    @ApiModelProperty("产品型号")
	@JsonProperty("gModel")
	private  String gModel;
	/**
      * 规格
      */
    @ApiModelProperty("规格")
	private  String specifications;
	/**
      * 克重
      */
    @ApiModelProperty("克重")
	private  BigDecimal gramWeight;
	/**
      * 供应商
      */
    @ApiModelProperty("供应商")
	private  String supplier;
	/**
      * 数量
      */
    @ApiModelProperty("数量")
	private  BigDecimal qty;
	/**
      * 单位
      */
    @ApiModelProperty("单位")
	private  String unit;
	/**
      * 单价
      */
    @ApiModelProperty("单价")
	private  BigDecimal unitPrice;
	/**
      * 金额
      */
    @ApiModelProperty("金额")
	private  BigDecimal amount;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String curr;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 最后修改人
      */
    @ApiModelProperty("最后修改人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend1;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend2;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend3;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend4;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend5;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend6;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend7;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend8;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend9;
	/**
      * 
      */
    @ApiModelProperty("")
	private  String extend10;
}
