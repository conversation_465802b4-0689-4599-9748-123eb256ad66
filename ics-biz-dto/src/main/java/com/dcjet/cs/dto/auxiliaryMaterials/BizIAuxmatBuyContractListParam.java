package com.dcjet.cs.dto.auxiliaryMaterials;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-5-29
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIAuxmatBuyContractListParam implements Serializable {
    private static final long serialVersionUID = 1L;

	/**
     * 主键
     */
	@ApiModelProperty("主键")
	private  String id;
	/**
     * 关联SID
     */
	@XdoSize(max = 36, message = "关联SID长度不能超过36位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("关联SID")
	private  String headId;
	/**
     * 商品名称
     */
	@NotEmpty(message="商品名称不能为空！")
	@XdoSize(max = 60, message = "商品名称长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	/**
     * 产品型号
     */
	@XdoSize(max = 100, message = "产品型号长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("产品型号")
	@JsonProperty("gModel")
	private  String gModel;
	/**
     * 规格
     */
	@XdoSize(max = 100, message = "规格长度不能超过100位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("规格")
	private  String specifications;
	/**
     * 克重
     */
	@Digits(integer = 15, fraction = 4, message = "克重必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("克重")
	private  BigDecimal gramWeight;
	/**
     * 供应商
     */
	@XdoSize(max = 200, message = "供应商长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("供应商")
	private  String supplier;
	/**
     * 数量
     */
	@NotNull(message="数量不能为空！")
	@Digits(integer = 13, fraction = 6, message = "数量必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("数量")
	private  BigDecimal qty;
	/**
     * 单位
     */
	@XdoSize(max = 30, message = "单位长度不能超过30位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("单位")
	private  String unit;
	/**
     * 单价
     */
	@NotNull(message="单价不能为空！")
	@Digits(integer = 11, fraction = 8, message = "单价必须为数字,整数位最大11位,小数最大8位!")
	@ApiModelProperty("单价")
	private  BigDecimal unitPrice;
	/**
     * 金额
     */
	@NotNull(message="金额不能为空！")
	@Digits(integer = 15, fraction = 4, message = "金额必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("金额")
	private  BigDecimal amount;
	/**
     * 币种
     */
	@NotEmpty(message="币种不能为空！")
	@XdoSize(max = 10, message = "币种长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("币种")
	private  String curr;
	/**
     * 创建人
     */
	@XdoSize(max = 10, message = "创建人长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
     * 创建时间
     */
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
     * 最后修改人
     */
	@XdoSize(max = 10, message = "最后修改人长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("最后修改人")
	private  String updateBy;
	/**
     * 最后修改时间
     */
	@ApiModelProperty("最后修改时间")
	private  Date updateTime;
	/**
     * 创建人部门编码
     */
	@XdoSize(max = 36, message = "创建人部门编码长度不能超过36位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
     * 企业编码
     */
	@XdoSize(max = 36, message = "企业编码长度不能超过36位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend1;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend2;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend3;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend4;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend5;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend6;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend7;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend8;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend9;
	/**
     * 
     */
	@XdoSize(max = 200, message = "长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("")
	private  String extend10;
}
