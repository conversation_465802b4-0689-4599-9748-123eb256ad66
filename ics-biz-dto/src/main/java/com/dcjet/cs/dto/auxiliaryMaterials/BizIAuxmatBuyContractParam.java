package com.dcjet.cs.dto.auxiliaryMaterials;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 *
 * <AUTHOR>
 * @date: 2025-5-28
 */
@Setter @Getter
@ApiModel(value = "传入参数")
public class BizIAuxmatBuyContractParam implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
    * 主键
    */
	@ApiModelProperty("主键")
    private String id;
	/**
     * 业务类型
     */
	@NotEmpty(message="业务类型不能为空！")
	@XdoSize(max = 60, message = "业务类型长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务类型")
	private  String businessType;
	/**
     * 购销合同号
     */
	@NotEmpty(message="购销合同号不能为空！")
	@XdoSize(max = 60, message = "购销合同号长度不能超过60位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("购销合同号")
	private  String contractNo;
	/**
     * 购销年份
     */
	@NotNull(message="购销年份不能为空！")
	@ApiModelProperty("购销年份")
	private  String contractYear;
	/**
     * 业务区分
     */
	@NotEmpty(message="业务区分不能为空！")
	@XdoSize(max = 20, message = "业务区分长度不能超过20位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("业务区分")
	private  String businessDistinction;
	/**
     * 备注
     */
	@XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String remark;
	/**
     * 版本号
     */
	@NotEmpty(message="版本号不能为空！")
	@XdoSize(max = 10, message = "版本号长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("版本号")
	private  String versionNo;
	/**
     * 单据状态
     */
	@NotEmpty(message="单据状态不能为空！")
	@ApiModelProperty("单据状态")
	private  String status;
	/**
     * 确认时间
     */
	@ApiModelProperty("确认时间")
	private  Date confirmTime;
	/**
     * 审批状态
     */
	@XdoSize(max = 10, message = "审批状态长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("审批状态")
	private  String apprStatus;
	/**
     * 创建人
     */
	@XdoSize(max = 10, message = "创建人长度不能超过10位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("创建人")
	private  String createBy;
	/**
     * 创建时间
     */
	@ApiModelProperty("创建时间")
	private  Date createTime;
	/**
    * 创建时间-开始
    */
	@ApiModelProperty("创建时间-开始")
	private String createTimeFrom;
	/**
    * 创建时间-结束
    */
	@ApiModelProperty("创建时间-结束")
    private String createTimeTo;
}
