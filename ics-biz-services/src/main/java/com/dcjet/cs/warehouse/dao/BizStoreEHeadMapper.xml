<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.warehouse.dao.BizStoreEHeadMapper">
    <resultMap id="bizStoreEHeadResultMap" type="com.dcjet.cs.warehouse.model.BizStoreEHead">
		<id column="sid" property="sid" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="create_user_name" property="createUserName" jdbcType="VARCHAR" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="update_user_name" property="updateUserName" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="extend1" property="extend1" jdbcType="VARCHAR" />
		<result column="extend2" property="extend2" jdbcType="VARCHAR" />
		<result column="extend3" property="extend3" jdbcType="VARCHAR" />
		<result column="extend4" property="extend4" jdbcType="VARCHAR" />
		<result column="extend5" property="extend5" jdbcType="VARCHAR" />
		<result column="extend6" property="extend6" jdbcType="VARCHAR" />
		<result column="extend7" property="extend7" jdbcType="VARCHAR" />
		<result column="extend8" property="extend8" jdbcType="VARCHAR" />
		<result column="extend9" property="extend9" jdbcType="VARCHAR" />
		<result column="extend10" property="extend10" jdbcType="VARCHAR" />
		<result column="store_e_no" property="storeENo" jdbcType="VARCHAR" />
		<result column="contract_no" property="contractNo" jdbcType="VARCHAR" />
		<result column="purchase_order_no" property="purchaseOrderNo" jdbcType="VARCHAR" />
		<result column="pur_sale_contract_no" property="purSaleContractNo" jdbcType="VARCHAR" />
		<result column="consignee" property="consignee" jdbcType="VARCHAR" />
		<result column="delivery_date" property="deliveryDate" jdbcType="TIMESTAMP" />
		<result column="product_amount_total" property="productAmountTotal" jdbcType="NUMERIC" />
		<result column="tariff_price" property="tariffPrice" jdbcType="NUMERIC" />
		<result column="insurance_fee" property="insuranceFee" jdbcType="NUMERIC" />
		<result column="agent_fee" property="agentFee" jdbcType="NUMERIC" />
		<result column="business_date" property="businessDate" jdbcType="TIMESTAMP" />
		<result column="send_finance" property="sendFinance" jdbcType="VARCHAR" />
		<result column="note" property="note" jdbcType="VARCHAR" />
		<result column="status" property="status" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="confirm_time" property="confirmTime" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     sid
     ,COALESCE(update_by,create_by) as createrBy
     ,COALESCE(update_user_name,create_user_name) as createrUserName
     ,COALESCE(update_time,create_time) as createrTime
     ,create_by
     ,create_time
     ,create_user_name
     ,update_by
     ,update_time
     ,update_user_name
     ,trade_code
     ,sys_org_code
     ,extend1
     ,extend2
     ,extend3
     ,extend4
     ,extend5
     ,extend6
     ,extend7
     ,extend8
     ,extend9
     ,extend10
     ,store_e_no
     ,contract_no
     ,purchase_order_no
     ,pur_sale_contract_no
     ,consignee
     ,delivery_date
     ,product_amount_total
     ,tariff_price
     ,insurance_fee
     ,agent_fee
     ,business_date
     ,send_finance
     ,note
     ,status
     ,head_id
     ,confirm_time
    </sql>
    <sql id="condition">
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizStoreEHeadResultMap" parameterType="com.dcjet.cs.warehouse.model.BizStoreEHead">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_store_e_head t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <select id="getStoreEHeadByHeadSid" resultType="com.dcjet.cs.warehouse.model.BizStoreEHead">
        select <include refid="Base_Column_List"/> from t_biz_store_e_head t where t.HEAD_ID = #{headId};
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_store_e_head t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
