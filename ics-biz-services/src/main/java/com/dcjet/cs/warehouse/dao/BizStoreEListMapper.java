package com.dcjet.cs.warehouse.dao;
import com.dcjet.cs.warehouse.model.BizStoreEList;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizStoreEList
* <AUTHOR>
* @date: 2025-5-22
*/
public interface BizStoreEListMapper extends Mapper<BizStoreEList> {
    /**
     * 查询获取数据
     * @param bizStoreEList
     * @return
     */
    List<BizStoreEList> getList(BizStoreEList bizStoreEList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
