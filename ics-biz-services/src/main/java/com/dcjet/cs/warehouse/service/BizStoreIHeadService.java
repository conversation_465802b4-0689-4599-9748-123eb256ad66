package com.dcjet.cs.warehouse.service;
import com.dcjet.cs.dec.model.BizIOrderHead;
import com.dcjet.cs.dto.dec.OrderHeadIsNextModuleDto;
import com.dcjet.cs.importedCigarettes.model.BizIPlan;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.warehouse.dao.BizStoreIListMapper;
import com.dcjet.cs.warehouse.model.BizStoreIList;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.warehouse.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.warehouse.dao.BizStoreIHeadMapper;
import com.dcjet.cs.warehouse.mapper.BizStoreIHeadDtoMapper;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Service
public class BizStoreIHeadService extends BaseService<BizStoreIHead> {
    @Resource
    private BizStoreIHeadMapper bizStoreIHeadMapper;
    @Resource
    private BizStoreIHeadDtoMapper bizStoreIHeadDtoMapper;
    @Override
    public Mapper<BizStoreIHead> getMapper() {
        return bizStoreIHeadMapper;
    }
    @Resource
    private BizStoreEHeadService bizStoreEHeadService;
    @Resource
    private BizStoreEListService bizStoreEListService;
    @Resource
    private BizStoreIListMapper bizStoreIListMapper;
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizStoreIHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizStoreIHeadDto>> getListPaged(BizStoreIHeadParam bizStoreIHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizStoreIHead bizStoreIHead = bizStoreIHeadDtoMapper.toPo(bizStoreIHeadParam);
        bizStoreIHead.setTradeCode(userInfo.getCompany());
        Page<BizStoreIHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizStoreIHeadMapper.getList(bizStoreIHead));
        List<BizStoreIHeadDto> bizStoreIHeadDtos = page.getResult().stream().map(head -> {
            BizStoreIHeadDto dto = bizStoreIHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizStoreIHeadDto>> paged = ResultObject.createInstance(bizStoreIHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizStoreIHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreIHeadDto insert(BizStoreIHeadParam bizStoreIHeadParam, UserInfoToken userInfo) {
        BizStoreIHead bizStoreIHead = bizStoreIHeadDtoMapper.toPo(bizStoreIHeadParam);
        bizStoreIHead.setTradeCode(userInfo.getCompany());
        int check = bizStoreIHeadMapper.checkKey(bizStoreIHead);
        if(check>0){
            throw new ErrorException(400, "入库回单编号已经存在！");
        }
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizStoreIHead.setSid(sid);
        bizStoreIHead.setCreateBy(userInfo.getUserNo());
        bizStoreIHead.setCreateTime(new Date());
        bizStoreIHead.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = bizStoreIHeadMapper.insert(bizStoreIHead);
        return  insertStatus > 0 ? bizStoreIHeadDtoMapper.toDto(bizStoreIHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizStoreIHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreIHeadDto update(BizStoreIHeadParam bizStoreIHeadParam, UserInfoToken userInfo) {
        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(bizStoreIHeadParam.getSid());
        bizStoreIHeadDtoMapper.updatePo(bizStoreIHeadParam, bizStoreIHead);
        bizStoreIHead.setUpdateBy(userInfo.getUserNo());
        bizStoreIHead.setUpdateTime(new Date());

        int check = bizStoreIHeadMapper.checkKey(bizStoreIHead);
        if(check>0){
            throw new ErrorException(400, "入库回单编号已经存在！");
        }
        // 更新数据
        int update = bizStoreIHeadMapper.updateByPrimaryKey(bizStoreIHead);
        return update > 0 ? bizStoreIHeadDtoMapper.toDto(bizStoreIHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizStoreIHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizStoreIHeadDto> selectAll(BizStoreIHeadParam exportParam, UserInfoToken userInfo) {
        BizStoreIHead bizStoreIHead = bizStoreIHeadDtoMapper.toPo(exportParam);
         bizStoreIHead.setTradeCode(userInfo.getCompany());
        List<BizStoreIHeadDto> bizStoreIHeadDtos = new ArrayList<>();
        List<BizStoreIHead> bizStoreIHeads = bizStoreIHeadMapper.getList(bizStoreIHead);
        if (CollectionUtils.isNotEmpty(bizStoreIHeads)) {
            bizStoreIHeadDtos = bizStoreIHeads.stream().map(head -> {
                BizStoreIHeadDto dto = bizStoreIHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizStoreIHeadDtos;
    }

    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("发送审批成功"));

        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreIHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (!CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizStoreIHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("请将进口计划操作确认再发送审批");
            return resultObject;
        }
        // 更新审批状态为2（审批中）
        bizStoreIHead.setApprStatus(CommonEnum.OrderApprStatusEnum.APPROVING.getValue());
        updateApprovalStatus(bizStoreIHead); // 调用更新审批状态的方法

        return resultObject;
    }
    @Transactional(rollbackFor = Exception.class)
    public void updateApprovalStatus(BizStoreIHead bizStoreIHead) {
        BizStoreIHead update = new BizStoreIHead();
        update.setSid(bizStoreIHead.getSid());
        update.setApprStatus(bizStoreIHead.getApprStatus());
        bizStoreIHeadMapper.updateByPrimaryKeySelective(update);
    }

    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("作废成功"));

        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreIHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        // 检查审批状态
        if (CommonEnum.OrderApprStatusEnum.APPROVING.getValue().equals(bizStoreIHead.getApprStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("审批中的数据不允许作废");
            return resultObject;
        }
        BizStoreIHead update = new BizStoreIHead();
        update.setSid(sid);
        update.setStatus(CommonEnum.OrderStatusEnum.CANCELLED.getValue());
        bizStoreIHeadMapper.updateByPrimaryKeySelective(update);

        return resultObject;
    }
    public ResultObject confirmStatus(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreIHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizStoreIHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }

//        BizStoreIHead update = new BizStoreIHead();
//        bizStoreIHead.setSid(sid);
        bizStoreIHead.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizStoreIHead.setConfirmTime(new Date());
        bizStoreIHead.setIsNext("1");
        bizStoreIHeadMapper.updateByPrimaryKeySelective(bizStoreIHead);
        resultObject.setData(bizStoreIHeadDtoMapper.toDto(bizStoreIHead));

        BizStoreIList bizStoreIList = new BizStoreIList();
        bizStoreIList.setHeadId(sid);
        List<BizStoreIList> iLists = bizStoreIListMapper.getList(bizStoreIList);

        //出库回单
        BizStoreEHeadParam bizStoreEHeadParam = new BizStoreEHeadParam();
        bizStoreEHeadParam.setHeadId(sid);
        bizStoreEHeadParam.setStoreENo(bizStoreIHead.getStoreINo());
        bizStoreEHeadParam.setContractNo(bizStoreIHead.getContractNo());
        bizStoreEHeadParam.setPurchaseOrderNo(bizStoreIHead.getPurchaseOrderNo());
        bizStoreEHeadParam.setProductAmountTotal(bizStoreIHead.getProductAmountTotal());
        bizStoreEHeadParam.setTariffPrice(bizStoreIHead.getTariffPrice());
        bizStoreEHeadParam.setInsuranceFee(bizStoreIHead.getInsuranceFee());
        bizStoreEHeadParam.setAgentFee(bizStoreIHead.getAgentFee());
        bizStoreEHeadParam.setBusinessDate(new Date());
        bizStoreEHeadParam.setSendFinance("0");
        bizStoreEHeadParam.setStatus("0");
        BizStoreEHeadDto insertE = bizStoreEHeadService.insert(bizStoreEHeadParam, userInfo);
        if(!ObjectUtils.isEmpty(insertE) && !ObjectUtils.isEmpty(iLists)){
            if(StringUtils.isNotBlank(insertE.getSid())){
                for (BizStoreIList iList : iLists) {
                    BizStoreEListParam listParam = new BizStoreEListParam();
                    listParam.setHeadId(insertE.getSid());
                    listParam.setGNameNo(iList.getGName());
                    listParam.setUnit(iList.getUnit());
                    listParam.setQtyDeli(iList.getQty());
                    listParam.setQtyIss(iList.getQty());
                    bizStoreEListService.insert(listParam,userInfo);
                }
            }
        }
        return resultObject;
    }

    public ResultObject redFlush(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("红冲成功"));

        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreIHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }

        BizStoreIHead update = new BizStoreIHead();
        update.setSid(sid);
        update.setRedFlush(CommonEnum.RED_FLUSH_ENUM.YES.getValue());
        bizStoreIHeadMapper.updateByPrimaryKeySelective(update);
        return resultObject;
    }

    public ResultObject checkIsNextModule(BizStoreIHeadParam params, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, "校验成功！");
        String sid = params.getSid();
        BizStoreIHead bizStoreIHead = bizStoreIHeadMapper.selectByPrimaryKey(sid);
        StoreIHeadIsNextDto isNextDto = new StoreIHeadIsNextDto();
        if (bizStoreIHead.getUpdateTime() != null){
            isNextDto.setShowBody(1);
        }else {
            isNextDto.setShowBody(0);
        }
        if (bizStoreIHead.getConfirmTime() != null){
            isNextDto.setShowBodyIsNext(1);
        }else {
            isNextDto.setShowBodyIsNext(0);
        }
        if (StringUtils.isNotBlank(bizStoreIHead.getIsNext()) && bizStoreIHead.getIsNext().equals("1")){
            isNextDto.setShowBodyStoreEHead(1);
        }else {
            isNextDto.setShowBodyStoreEHead(0);
        }
        resultObject.setData(isNextDto);
        return resultObject;
    }
}
