package com.dcjet.cs.warehouse.service;
import com.dcjet.cs.dec.model.BizIOrderList;
import com.dcjet.cs.dto.dec.BizIOrderListDto;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.warehouse.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.warehouse.dao.BizStoreEListMapper;
import com.dcjet.cs.warehouse.mapper.BizStoreEListDtoMapper;
import com.dcjet.cs.warehouse.model.BizStoreEList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Service
public class BizStoreEListService extends BaseService<BizStoreEList> {
    @Resource
    private BizStoreEListMapper bizStoreEListMapper;
    @Resource
    private BizStoreEListDtoMapper bizStoreEListDtoMapper;
    @Override
    public Mapper<BizStoreEList> getMapper() {
        return bizStoreEListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizStoreEListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizStoreEListDto>> getListPaged(BizStoreEListParam bizStoreEListParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizStoreEList bizStoreEList = bizStoreEListDtoMapper.toPo(bizStoreEListParam);
        bizStoreEList.setTradeCode(userInfo.getCompany());
        Page<BizStoreEList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizStoreEListMapper.getList(bizStoreEList));
        List<BizStoreEListDto> bizStoreEListDtos = page.getResult().stream().map(head -> {
            BizStoreEListDto dto = bizStoreEListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizStoreEListDto>> paged = ResultObject.createInstance(bizStoreEListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizStoreEListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreEListDto insert(BizStoreEListParam bizStoreEListParam, UserInfoToken userInfo) {
        BizStoreEList bizStoreEList = bizStoreEListDtoMapper.toPo(bizStoreEListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizStoreEList.setSid(sid);
        bizStoreEList.setCreateBy(userInfo.getUserNo());
        bizStoreEList.setCreateTime(new Date());
        bizStoreEList.setTradeCode(userInfo.getCompany());
        // 新增数据
        int insertStatus = bizStoreEListMapper.insert(bizStoreEList);
        return  insertStatus > 0 ? bizStoreEListDtoMapper.toDto(bizStoreEList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizStoreEListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreEListDto update(BizStoreEListParam bizStoreEListParam, UserInfoToken userInfo) {
        BizStoreEList bizStoreEList = bizStoreEListMapper.selectByPrimaryKey(bizStoreEListParam.getSid());
        bizStoreEListDtoMapper.updatePo(bizStoreEListParam, bizStoreEList);
        bizStoreEList.setUpdateBy(userInfo.getUserNo());
        bizStoreEList.setUpdateTime(new Date());
        // 更新数据
        int update = bizStoreEListMapper.updateByPrimaryKey(bizStoreEList);
        return update > 0 ? bizStoreEListDtoMapper.toDto(bizStoreEList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizStoreEListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizStoreEListDto> selectAll(BizStoreEListParam exportParam, UserInfoToken userInfo) {
        BizStoreEList bizStoreEList = bizStoreEListDtoMapper.toPo(exportParam);
         bizStoreEList.setTradeCode(userInfo.getCompany());
        List<BizStoreEListDto> bizStoreEListDtos = new ArrayList<>();
        List<BizStoreEList> bizStoreELists = bizStoreEListMapper.getList(bizStoreEList);
        if (CollectionUtils.isNotEmpty(bizStoreELists)) {
            bizStoreEListDtos = bizStoreELists.stream().map(head -> {
                BizStoreEListDto dto = bizStoreEListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizStoreEListDtos;
    }

    public ResultObject getListBySid(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");
        BizStoreEList bizStoreEList = bizStoreEListMapper.selectByPrimaryKey(sid);
        BizStoreEListDto dto = bizStoreEListDtoMapper.toDto(bizStoreEList);
        resultObject.setData(dto);
        return resultObject;
    }
}
