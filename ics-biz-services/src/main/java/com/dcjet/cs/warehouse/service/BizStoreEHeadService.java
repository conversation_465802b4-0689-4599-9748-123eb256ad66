package com.dcjet.cs.warehouse.service;
import com.dcjet.cs.dec.model.BizIPurchaseHead;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadDto;
import com.dcjet.cs.dto.dec.BizIPurchaseHeadParam;
import com.dcjet.cs.util.CommonEnum;
import com.dcjet.cs.warehouse.model.BizStoreEList;
import com.dcjet.cs.warehouse.model.BizStoreIHead;
import com.xdo.common.exception.ErrorException;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.warehouse.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.warehouse.dao.BizStoreEHeadMapper;
import com.dcjet.cs.warehouse.mapper.BizStoreEHeadDtoMapper;
import com.dcjet.cs.warehouse.model.BizStoreEHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-22
 */
@Service
public class BizStoreEHeadService extends BaseService<BizStoreEHead> {
    @Resource
    private BizStoreEHeadMapper bizStoreEHeadMapper;
    @Resource
    private BizStoreEHeadDtoMapper bizStoreEHeadDtoMapper;
    @Override
    public Mapper<BizStoreEHead> getMapper() {
        return bizStoreEHeadMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizStoreEHeadParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizStoreEHeadDto>> getListPaged(BizStoreEHeadParam bizStoreEHeadParam, PageParam pageParam, UserInfoToken userInfo) {
        // 启用分页查询
        BizStoreEHead bizStoreEHead = bizStoreEHeadDtoMapper.toPo(bizStoreEHeadParam);
        bizStoreEHead.setTradeCode(userInfo.getCompany());
        Page<BizStoreEHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizStoreEHeadMapper.getList(bizStoreEHead));
        List<BizStoreEHeadDto> bizStoreEHeadDtos = page.getResult().stream().map(head -> {
            BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizStoreEHeadDto>> paged = ResultObject.createInstance(bizStoreEHeadDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizStoreEHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreEHeadDto insert(BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        BizStoreEHead bizStoreEHead = bizStoreEHeadDtoMapper.toPo(bizStoreEHeadParam);
        bizStoreEHead.setTradeCode(userInfo.getCompany());
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizStoreEHead.setSid(sid);
        bizStoreEHead.setCreateBy(userInfo.getUserNo());
        bizStoreEHead.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizStoreEHeadMapper.insert(bizStoreEHead);
        return  insertStatus > 0 ? bizStoreEHeadDtoMapper.toDto(bizStoreEHead) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizStoreEHeadParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizStoreEHeadDto update(BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.selectByPrimaryKey(bizStoreEHeadParam.getSid());
        bizStoreEHeadDtoMapper.updatePo(bizStoreEHeadParam, bizStoreEHead);
        bizStoreEHead.setUpdateBy(userInfo.getUserNo());
        bizStoreEHead.setUpdateTime(new Date());
        // 更新数据
        int update = bizStoreEHeadMapper.updateByPrimaryKey(bizStoreEHead);
        return update > 0 ? bizStoreEHeadDtoMapper.toDto(bizStoreEHead) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizStoreEHeadMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizStoreEHeadDto> selectAll(BizStoreEHeadParam exportParam, UserInfoToken userInfo) {
        BizStoreEHead bizStoreEHead = bizStoreEHeadDtoMapper.toPo(exportParam);
         bizStoreEHead.setTradeCode(userInfo.getCompany());
        List<BizStoreEHeadDto> bizStoreEHeadDtos = new ArrayList<>();
        List<BizStoreEHead> bizStoreEHeads = bizStoreEHeadMapper.getList(bizStoreEHead);
        if (CollectionUtils.isNotEmpty(bizStoreEHeads)) {
            bizStoreEHeadDtos = bizStoreEHeads.stream().map(head -> {
                BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizStoreEHeadDtos;
    }

    public ResultObject getListBySid(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true,"获取成功！");
        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.selectByPrimaryKey(sid);
        BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(bizStoreEHead);
        resultObject.setData(dto);
        return resultObject;
    }

    public ResultObject<BizStoreEHeadDto> getStoreEHeadByHeadSid(BizStoreEHeadParam bizStoreEHeadParam, UserInfoToken userInfo) {
        ResultObject<BizStoreEHeadDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.getStoreEHeadByHeadSid(bizStoreEHeadParam.getHeadId());
        if (bizStoreEHead != null) {
            BizStoreEHeadDto dto = bizStoreEHeadDtoMapper.toDto(bizStoreEHead);
            resultObject.setData(dto);
        }
        return resultObject;
    }

    public ResultObject confirmStatus(String sid, UserInfoToken userInfo) {
        ResultObject resultObject = ResultObject.createInstance(true, xdoi18n.XdoI18nUtil.t("确认成功"));

        BizStoreEHead bizStoreEHead = bizStoreEHeadMapper.selectByPrimaryKey(sid);
        if (bizStoreEHead == null) {
            resultObject.setSuccess(false);
            resultObject.setMessage("数据不存在");
            return resultObject;
        }
        if (CommonEnum.OrderStatusEnum.CONFIRMED.getValue().equals(bizStoreEHead.getStatus())) {
            resultObject.setSuccess(false);
            resultObject.setMessage("该数据已经确认，无需重复操作");
            return resultObject;
        }

//        BizStoreEHead update = new BizStoreEHead();
//        bizStoreEHead.setSid(sid);
        bizStoreEHead.setStatus(CommonEnum.OrderStatusEnum.CONFIRMED.getValue());
        bizStoreEHead.setConfirmTime(new Date());
        bizStoreEHeadMapper.updateByPrimaryKeySelective(bizStoreEHead);
        resultObject.setData(bizStoreEHead);
        return resultObject;
    }
}
