<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.bi.dao.ExpenseIListMapper">
    <sql id="Base_Column_List">
        SID
        ,HEAD_ID
        ,STATE
        ,CONTRACT_NUMBER
        ,INVOICE_NUMBER
        ,EXPENSE_TYPE
        ,QUANTITY
        ,TAX_AMOUNT
        ,NO_TAX_AMOUNT
        ,EXPENSE_AMOUNT
        ,TRADE_CODE
        ,INSERT_USER
        ,INSERT_TIME
        ,UPDATE_USER
        ,UPDATE_TIME
        ,PURCHASE_NUMBER
        ,PRODUCT_NAME
    </sql>
    <sql id="condition">

    </sql>
    <delete id="deleteBySids">
        delete from T_BIZ_EXPENSE_I_LIST l
        where l.CONTRACT_NUMBER in (select l.CONTRACT_NUMBER
        from T_BIZ_EXPENSE_I_LIST l
        where l.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)
        or l.purchase_number in (select l.purchase_number
        from T_BIZ_EXPENSE_I_LIST l
        where l.sid in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>)
    </delete>
    <select id="getList" resultType="com.dcjet.cs.bi.model.ExpenseIList">
        select
        <include refid="Base_Column_List"/>
            from T_BIZ_EXPENSE_I_LIST
        where head_id = #{headId}
    </select>
    <select id="getContractList" resultType="com.dcjet.cs.dto.bi.CostIContractParam">
        select
            l.sid as sid,
            l.PRODUCT_GRADE,
            l.CONTRACT_NO,
            h.ORDER_NO,
            h.PARTY_A,
            h.PARTY_B,
            l.CURR,
            l.DEC_TOTAL,
            l.QTY,
            l.UNIT,
            m.MERCHANDISE_CATEGORIES,
            rl.INVOICE_NUMBER,
            ph.purchase_order_no
        from T_BIZ_I_ORDER_LIST l
        left join T_BIZ_I_ORDER_HEAD h on h.sid = l.head_id
        left join T_BIZ_I_Purchase_HEAD ph on ph.HEAD_ID = h.SID
        left join T_BIZ_I_Purchase_LIST pl on pl.CONTRACT_LIST_ID = l.CONTRACT_LIST_ID  and ph.sid = pl.head_id
        left join t_biz_warehouse_receipt_list rl on rl.sid = pl.sid
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE
        where
            l.trade_code = #{tradeCode} and
            pl.HEAD_ID = ph.SID and
            h.data_status  in ('1')
            <if test="contractNo != null and contractNo != ''">
                and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                and h.ORDER_NO   like concat(concat('%',#{orderNo}),'%')
            </if>
            <if test="sids != null and sids.size() > 0">
                and l.head_id in
                <foreach collection="sids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
    </select>
    <select id="getShippingOrderList" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        select
            ol.sid as sid,
            h.purchase_order_no,
            ol.CONTRACT_NO,
            hh.ORDER_NO,
            hh.PARTY_A,
            hh.PARTY_B,
            l.PRODUCT_GRADE,
            l.CURR,
            l.DEC_TOTAL,
            l.DEC_PRICE,
            l.QTY,
            l.UNIT,
            m.MERCHANDISE_CATEGORIES,
            rl.INVOICE_NUMBER
        from T_BIZ_I_Purchase_LIST l
                 left join T_BIZ_I_Purchase_HEAD h  on h.sid = l.head_id
                 left join T_BIZ_I_ORDER_HEAD hh on hh.sid = h.HEAD_ID
                 left join t_biz_warehouse_receipt_list rl on rl.sid = l.sid
                 left join T_BIZ_I_ORDER_LIST ol on ol.contract_list_id = l.contract_list_id and ol.HEAD_ID = hh.sid
                 left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE
        where
        l.trade_code = #{tradeCode} and
        hh.data_status  in ('1') and
        h.data_status  in ('1')
        <if test="contractNo != null and contractNo != ''">
            and hh.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and hh.purchase_order_no  like concat(concat('%',#{orderNo}),'%')
        </if>
        <if test="productGrade != null and productGrade != ''">
            and l.PRODUCT_GRADE = #{productGrade}
        </if>
        <if test="sids != null and sids.size() > 0">
            and l.head_id in
            <foreach collection="sids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getSumDataByInvoiceSell" resultType="com.dcjet.cs.bi.model.ExpenseIList">
        select
            sum(EXPENSE_AMOUNT)  as EXPENSE_AMOUNT
        from T_BIZ_EXPENSE_I_LIST
        where HEAD_ID = #{headId};
    </select>
    <select id="getContractHead" resultType="com.dcjet.cs.dto.bi.CostIContractParam">
        select
                l.HEAD_ID as sid,
                max(ph.purchase_order_no) as purchase_order_no,
                max(l.PRODUCT_GRADE) as PRODUCT_GRADE,
                max(h.CONTRACT_NO) as CONTRACT_NO,
                max(h.ORDER_NO) as ORDER_NO,
                max(h.PARTY_A) as PARTY_A,
                max(h.PARTY_B) as PARTY_B,
                max(l.CURR) as CURR,
                sum(l.DEC_TOTAL) as DEC_TOTAL,
                sum(l.QTY) as QTY,
                max(l.UNIT) as UNIT,
                max(m.MERCHANDISE_CATEGORIES) as MERCHANDISE_CATEGORIES,
                LISTAGG( DISTINCT
                pl.invoice_no, ',') WITHIN GROUP(ORDER BY pl.invoice_no) as INVOICE_NUMBER
            from T_BIZ_I_ORDER_HEAD h
            left join T_BIZ_I_ORDER_LIST l  on h.sid = l.head_id
            left join T_BIZ_I_Purchase_LIST pl on pl.CONTRACT_LIST_ID = l.CONTRACT_LIST_ID
            left join T_BIZ_I_Purchase_HEAD ph on ph.HEAD_ID = h.SID
            left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE
        where
        h.trade_code = #{tradeCode} and
        h.data_status in ('1') and pl.HEAD_ID = ph.SID and
        (select count(1)
        from T_BIZ_COST_TYPE t
        where t.COMMON_FLAG like '%1%'
        and t.TRADE_CODE =  #{tradeCode}
        and NOT EXISTS (
        SELECT 1
        FROM T_BIZ_EXPENSE_I_LIST el
        WHERE el.EXPENSE_TYPE = t.PARAM_CODE
        AND el.state IN ('0','1')
        AND el.purchase_number = h.purchase_order_no
        )) != 0
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="orderNo != null and orderNo != ''">
            and h.ORDER_NO   like concat(concat('%',#{orderNo}),'%')
        </if>
        group by l.HEAD_ID
    </select>
    <select id="getShippingOrderHead" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        select
        l.HEAD_ID as sid,
        max(h.purchase_order_no) as purchase_order_no,
        max(hh.CONTRACT_NO) as CONTRACT_NO,
        max(hh.ORDER_NO) as ORDER_NO,
        max(hh.PARTY_A) as PARTY_A,
        max(hh.PARTY_B) as PARTY_B,
        max(l.PRODUCT_GRADE) as PRODUCT_GRADE,
        max(l.CURR) as CURR,
        sum(l.DEC_TOTAL) as DEC_TOTAL,
        sum(l.DEC_PRICE) as DEC_PRICE,
        sum(l.QTY) as QTY,
        max(l.UNIT) as UNIT,
        max(m.MERCHANDISE_CATEGORIES) as MERCHANDISE_CATEGORIES,
        LISTAGG( DISTINCT
        l.invoice_no, ',') WITHIN GROUP(ORDER BY l.invoice_no) as INVOICE_NUMBER
        from T_BIZ_I_Purchase_HEAD h
        left join T_BIZ_I_Purchase_LIST l  on h.sid = l.head_id
        left join T_BIZ_I_ORDER_HEAD hh on hh.sid = h.HEAD_ID
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE
        where
        h.trade_code = #{tradeCode} and
        hh.data_status in ('1') and
        h.data_status in ('1') and
            (select count(1)
        from T_BIZ_COST_TYPE t
        where t.COMMON_FLAG like '%1%'
        and t.TRADE_CODE =  #{tradeCode}
        and NOT EXISTS (
        SELECT 1
        FROM T_BIZ_EXPENSE_I_LIST el
        WHERE el.EXPENSE_TYPE = t.PARAM_CODE
        AND el.state IN ('0','1')
        AND el.purchase_number = h.purchase_order_no
        )) != 0
        <if test="contractNo != null and contractNo != ''">
            and hh.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and hh.purchase_order_no  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        <if test="productGrade != null and productGrade != ''">
            and l.PRODUCT_GRADE = #{productGrade}
        </if>
        group by l.HEAD_ID
    </select>
    <select id="getContractHeadPayment" resultType="com.dcjet.cs.dto.bi.CostIContractParam">
        select
        l.HEAD_ID as sid,
        max(l.PRODUCT_GRADE) as PRODUCT_GRADE,
        max(h.CONTRACT_NO) as CONTRACT_NO,
        max(h.ORDER_NO) as ORDER_NO,
        max(h.PARTY_A) as PARTY_A,
        max(h.PARTY_B) as PARTY_B,
        max(l.CURR) as CURR,
        sum(l.DEC_TOTAL) as DEC_TOTAL,
        sum(l.QTY) as QTY,
        max(l.UNIT) as UNIT,
        max(m.MERCHANDISE_CATEGORIES) as MERCHANDISE_CATEGORIES,
        LISTAGG(DISTINCT rl.INVOICE_NUMBER, ',') WITHIN GROUP(ORDER BY rl.INVOICE_NUMBER) as INVOICE_NUMBER
        from T_BIZ_I_ORDER_HEAD h
        left join T_BIZ_I_ORDER_LIST l  on h.sid = l.head_id
        left join T_BIZ_I_Purchase_HEAD ph on ph.HEAD_ID = h.SID
        left join T_BIZ_I_Purchase_LIST pl on pl.CONTRACT_LIST_ID = l.CONTRACT_LIST_ID and ph.sid = pl.head_id
        left join t_biz_warehouse_receipt_list rl on rl.sid = pl.sid
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE
        where
        NOT EXISTS (
        SELECT 1
        FROM T_BIZ_PAYMENT_NOTIFY_LIST nl
        JOIN T_BIZ_PAYMENT_NOTIFY_HEAD nh ON nl.head_id = nh.sid
        left join T_BIZ_I_ORDER_LIST ol on ol.contract_list_id = nl.sid
        WHERE nh.DOC_STATUS IN ('0','1') AND nl.turnover_sid = l.sid
        )
        and
        h.trade_code = #{tradeCode} and
        h.data_status in ('1') and pl.HEAD_ID = ph.SID
        <if test="contractNo != null and contractNo != ''">
            and h.CONTRACT_NO   like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="orderNo != null and orderNo != ''">
            and h.ORDER_NO   like concat(concat('%',#{orderNo}),'%')
        </if>
        group by l.HEAD_ID
        order by MAX(COALESCE(h.UPDATE_TIME,h.INSERT_TIME)) desc
    </select>
    <select id="getShippingOrderHeadPayment" resultType="com.dcjet.cs.dto.bi.CostIShippingOrderParam">
        select
        l.HEAD_ID as sid,
        max(h.purchase_order_no) as purchase_order_no,
        max(hh.CONTRACT_NO) as CONTRACT_NO,
        max(hh.ORDER_NO) as ORDER_NO,
        max(hh.PARTY_A) as PARTY_A,
        max(hh.PARTY_B) as PARTY_B,
        max(l.PRODUCT_GRADE) as PRODUCT_GRADE,
        max(l.CURR) as CURR,
        sum(l.DEC_TOTAL) as DEC_TOTAL,
        sum(l.DEC_PRICE) as DEC_PRICE,
        sum(l.QTY) as QTY,
        max(l.UNIT) as UNIT,
        max(m.MERCHANDISE_CATEGORIES) as MERCHANDISE_CATEGORIES,
        LISTAGG(DISTINCT rl.INVOICE_NUMBER, ',') WITHIN GROUP(ORDER BY rl.INVOICE_NUMBER) as INVOICE_NUMBER
        from T_BIZ_I_Purchase_HEAD h
        left join T_BIZ_I_Purchase_LIST l  on h.sid = l.head_id
        left join T_BIZ_I_ORDER_HEAD hh on hh.sid = h.HEAD_ID
        left join T_BIZ_I_ORDER_LIST ol on ol.contract_list_id = l.contract_list_id and ol.HEAD_ID = hh.sid
        left join t_biz_warehouse_receipt_list rl on rl.sid = l.sid
        left join T_BIZ_MATERIAL_INFORMATION m on m.G_NAME = l.PRODUCT_GRADE
        where
        NOT EXISTS (
        SELECT 1
        FROM T_BIZ_PAYMENT_NOTIFY_LIST nl
        JOIN T_BIZ_PAYMENT_NOTIFY_HEAD nh ON nl.head_id = nh.sid
        left join T_BIZ_I_ORDER_LIST ool on ool.sid = nl.turnover_sid
        left join T_BIZ_I_Purchase_HEAD pph on pph.HEAD_ID = ool.HEAD_ID
        left join T_BIZ_I_Purchase_LIST pl on pl.contract_list_id = ool.contract_list_id and pph.sid = pl.HEAD_ID
        WHERE nh.DOC_STATUS IN ('0','1') AND nl.turnover_sid = ol.sid
        )
        AND
        h.trade_code = #{tradeCode} and
        hh.data_status in ('1') and
        h.data_status in ('1')
        <if test="contractNo != null and contractNo != ''">
            and hh.CONTRACT_NO  like concat(concat('%',#{contractNo}),'%')
        </if>
        <if test="purchaseOrderNo != null and purchaseOrderNo != ''">
            and h.purchase_order_no  like concat(concat('%',#{purchaseOrderNo}),'%')
        </if>
        <if test="productGrade != null and productGrade != ''">
            and l.PRODUCT_GRADE = #{productGrade}
        </if>
        group by l.HEAD_ID
        order by MAX(COALESCE(h.UPDATE_TIME,h.INSERT_TIME)) desc
    </select>
    <select id="getCostType" resultType="com.dcjet.cs.dto.params.CostTypeDto">
        select PARAM_CODE,COST_NAME,customer_Supplier
        from T_BIZ_COST_TYPE t
        where t.COMMON_FLAG like '%1%'
          and t.TRADE_CODE =  #{tradeCode}
          and t.PARAM_CODE not in (select
                                       EXPENSE_TYPE
                                   from T_BIZ_EXPENSE_I_LIST l
                                   where l.state in ('0','1') and l.purchase_number = #{purchaseOrderNo})
    </select>

    <select id="getByHeadId" resultType="com.dcjet.cs.bi.model.ExpenseIList">
        select
        <include refid="Base_Column_List"/>
        , BAR_CODE
        , COST_NAME
        from (select * from T_BIZ_EXPENSE_I_LIST where HEAD_ID = #{headId}) EX_LIST
        left join (select * from
        (select G_NAME, BAR_CODE, row_number() over (partition by G_NAME order by G_NAME) as RN from
        T_BIZ_MATERIAL_INFORMATION where TRADE_CODE = #{tradeCode}) MAT
        where RN = 1) MAT_INFO on EX_LIST.PRODUCT_NAME = MAT_INFO.G_NAME
        left join (select * from
        (select PARAM_CODE, COST_NAME, row_number() over (partition by PARAM_CODE order by PARAM_CODE) as RN from
        T_BIZ_COST_TYPE where TRADE_CODE = #{tradeCode} and COMMON_FLAG like '%1%') COST
        where RN = 1) COST_TYPE on EX_LIST.EXPENSE_TYPE = COST_TYPE.PARAM_CODE
    </select>
</mapper>
