package com.dcjet.cs.dec.service;

import com.dcjet.cs.Utils.NumberFormatterUtils;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;
import com.dcjet.cs.bi.dao.BiClientInformationMapper;
import com.dcjet.cs.bi.dao.BizMaterialInformationMapper;
import com.dcjet.cs.bi.dao.BizMerchantMapper;
import com.dcjet.cs.bi.model.BiClientInformation;
import com.dcjet.cs.bi.model.BizMaterialInformation;
import com.dcjet.cs.bi.model.BizMerchant;
import com.dcjet.cs.common.service.CommonService;
import com.dcjet.cs.common.service.ExportService;

import com.dcjet.cs.common.service.ExportService;
import com.dcjet.cs.dec.dao.*;
import com.dcjet.cs.dec.mapper.BizIOrderHeadDtoMapper;
import com.dcjet.cs.dec.mapper.BizIWarehouseReceiptHeadDtoMapper;
import com.dcjet.cs.dec.mapper.BizIWarehouseReceiptListDtoMapper;
import com.dcjet.cs.dec.model.*;
import com.dcjet.cs.dto.dec.*;
import com.dcjet.cs.params.dao.EnterpriseRateMapper;
import com.dcjet.cs.params.dao.RateTableMapper;
import com.dcjet.cs.params.dao.StorehouseMapper;
import com.dcjet.cs.params.model.EnterpriseRate;
import com.dcjet.cs.params.model.RateTable;
import com.dcjet.cs.params.model.Storehouse;
import com.dcjet.cs.service.ThirdPartyDbService;
import com.dcjet.cs.util.ExcelMerger;
import com.dcjet.cs.util.variable.CommonVariable;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import dm.jdbc.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.io.MemoryUsageSetting;
import com.dcjet.cs.util.RMBConverterUtil;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.PrintSetup;

import javax.annotation.Resource;
import java.beans.Transient;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * BizIOrderHead业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2025-03-07 15:37:18
 * 翻译使用：throw new ErrorException(400, XdoI18nUtil.t("xxxxxxxxxx"));
 */
@Service
public class BizIWarehouseReceiptHeadService extends BaseService<BizIWarehouseReceiptHead> {

    private static final Logger log = LoggerFactory.getLogger(BizIWarehouseReceiptHeadService.class);

    @Resource
    private ThirdPartyDbService thirdPartyDbService;
    @Resource
    private BizIWarehouseReceiptHeadMapper mapper;
    @Resource
    private BizMaterialInformationMapper bizMaterialInformationMapper;
    @Resource
    private BiClientInformationMapper biClientInformationMapper;
    @Resource
    private StorehouseMapper storehouseMapper;
    @Resource
    private BizIWarehouseReceiptListMapper bizIWarehouseReceiptListMapper;
    @Resource
    private BaseInfoCustomerParamsMapper baseInfoCustomerParamsMapper;
    @Resource
    private BizIReceiptHeadMapper bizIReceiptHeadMapper;
    @Resource
    private EnterpriseRateMapper enterpriseRateMapper;
    @Resource
    private BizIReceiptListMapper bizIReceiptListMapper;

    @Resource
    private BizISellHeadMapper bizISellHeadMapper;

    @Resource
    private BizISellListMapper bizISellListMapper;


    @Resource
    private BizIWarehouseReceiptListMapper listMapper;

    @Resource
    private BizIWarehouseReceiptHeadDtoMapper dtoMapper;

    @Override
    public Mapper<BizIWarehouseReceiptHead> getMapper() {
        return mapper;
    }

    @Resource
    private BizIPurchaseHeadMapper bizIPurchaseHeadMapper;

    @Resource
    private BizIPurchaseListMapper bizIPurchaseListMapper;

    @Resource
    private BizIPurchaseListBoxMapper bizIPurchaseListBoxMapper;

    @Resource
    private BizIOrderHeadMapper bizIOrderHeadMapper;
    @Resource
    private ExportService exportService;
    @Resource
    private BizMerchantMapper bizMerchantMapper;


    @Resource
    private BizIWarehouseReceiptListDtoMapper bizIWarehouseReceiptListDtoMapper;

    /**
     * 获取分页信息
     *
     * @param param 查询参数
     */
    public ResultObject<BizIWarehouseReceiptHeadDto> getListPaged(BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) {

        ResultObject<BizIWarehouseReceiptHeadDto> resultObject = ResultObject.createInstance(true,"获取成功！");
        BizIWarehouseReceiptHead head = mapper.getList(param.getParentId());
        if (head != null) {
            BizIWarehouseReceiptHeadDto dto = dtoMapper.toDto(head);
            resultObject.setData(dto);
        }
        return resultObject;
    }

    /**
     * 修改记录
     *
     * @param param 更新参数
     * @param userInfo                用户信息
     * @return 更新后的DTO对象
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIWarehouseReceiptHeadDto update(BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) {
        BizIWarehouseReceiptHead head = mapper.selectByPrimaryKey(param.getSid());

        dtoMapper.updatePo(param, head);
        head.setUpdateUserName(userInfo.getUserName());
        head.setUpdateUser(userInfo.getUserNo());
        head.setUpdateTime(new Date());

        head.setInsertUserName(userInfo.getUserName());
        head.setInsertUser(userInfo.getUserNo());
        head.setInsertTime(new Date());
        head.setTradeCode(userInfo.getCompany());
        //拓展字段三 1表示保存成功，可以进行确定操作
        head.setExtend3("1");
        //校验进仓编号是否重复
        BizIWarehouseReceiptHead check=mapper.checkExists(head.getWarehouseEntryNumber(),head.getSid());
        if (null!=check){
            throw new RuntimeException("进仓编号已存在！");
        }

        // 更新数据
        int update = mapper.updateByPrimaryKey(head);




        // 更新入库回单表体
        if (CollectionUtils.isNotEmpty(param.getWarehouseReceiptListParams())) {
            List<BizIWarehouseReceiptListParam> params = param.getWarehouseReceiptListParams();
            // 校验每一条数据
            for (int i = 0; i < params.size(); i++) {
                BizIWarehouseReceiptListParam item = params.get(i);
                String message = item.canSubmit(i+1);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(message)){
                    throw new ErrorException(400, XdoI18nUtil.t(message));
                }
                // 循环更新表体数据
                BizIWarehouseReceiptList po = bizIWarehouseReceiptListDtoMapper.toPo(item);
                log.error("修改的外币货价：{}",po.getForeignPrices());
                bizIWarehouseReceiptListMapper.updateByPrimaryKey(po);
            }
        }


        // 表体更新完成  后 根据表头卖出价 计算表体 人民币单价和货价
        if (null!=head.getSellingRate()){
            //查询全部表体
            BizIWarehouseReceiptList list = new BizIWarehouseReceiptList();
            list.setParentId(head.getSid());
            list.setTradeCode(userInfo.getCompany());
            List<BizIWarehouseReceiptList> listDatas = listMapper.getList(list);
            for (BizIWarehouseReceiptList data:listDatas) {
                //人民币单价
                data.setRmbUnitPrice(head.getSellingRate().multiply(data.getForeignUnitPrice()).setScale(8, RoundingMode.HALF_UP));
                //人民币货价
                // 计算外币货价：单价 * 数量 * (1 - 折扣率/100)
                BigDecimal amount = data.getRmbUnitPrice().multiply(data.getQty());
                if (head.getDiscountRate() != null) {
                    amount = amount.multiply(BigDecimal.ONE.subtract(head.getDiscountRate().divide(BigDecimal.valueOf(100))));
                }
                data.setRmbPrices(amount);
                //商品金额小计
                data.setProducAmount(data.getRmbPrices());
                listMapper.updateByPrimaryKey(data);
            }

        }



        return update > 0 ? dtoMapper.toDto(head) : null;
    }

    public List<String> selectWarehouseEntryNumber(String status, UserInfoToken userInfo) {
        return mapper.selectWarehouseEntryNumber(status,userInfo.getCompany());
    }

    public List<String> selectladingNumber(String status, UserInfoToken userInfo) {
        return mapper.selectladingNumber(status,userInfo.getCompany());
    }

    @Transient
    public BizIPurchaseHead generateBizIWarehouseReceipt(BizIWarehouseReceiptHeadParam bizIWarehouseReceiptHeadParam, UserInfoToken userInfo) {
        //先删除已经生成的入库单数据
        BizIWarehouseReceiptHead oldhead = mapper.getList(bizIWarehouseReceiptHeadParam.getParentId());
        if (null!=oldhead){
            mapper.deleteHeadAndList(bizIWarehouseReceiptHeadParam.getParentId(),oldhead.getSid());
        }
        String parentId = bizIWarehouseReceiptHeadParam.getParentId();
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(parentId);
        //查询出进货信息表头和表体
        BizIPurchaseHead bizIPurchaseHead = bizIPurchaseHeadMapper.getEditDataByHeadId(parentId);

        BizIPurchaseList bizIPurchaseList =new BizIPurchaseList();
        bizIPurchaseList.setTradeCode(userInfo.getCompany());
        bizIPurchaseList.setHeadId(bizIPurchaseHead.getSid());
        List<BizIPurchaseList> bizIPurchaseLists = bizIPurchaseListMapper.getList(bizIPurchaseList);

        BizIWarehouseReceiptHead head=new BizIWarehouseReceiptHead();
        //入库回单编号 同进货单号
        head.setWarehouseReceiptNumber(bizIPurchaseHead.getPurchaseOrderNo());
        //合同号
        String contractNumbers=mapper.selectContract(bizIOrderHead.getSid());
        if (!StringUtils.isEmpty(contractNumbers)){
           String string= Arrays.stream(contractNumbers.split(","))
                    .map(String::trim)        // 清理空格（可选）
                    .filter(s -> !s.isEmpty()) // 过滤空字符串（可选）
                    .distinct()               // 去重（按首次出现顺序）
                    .collect(Collectors.joining(","));
           //合同号
           head.setContractNumber(string);
        }

        //订单号
        head.setOrderNo(bizIPurchaseHead.getOrderNo());
        //发送用友
        head.setSendToYongyou("0");
        //进仓编号
        String currentYear = new SimpleDateFormat("yy").format(new Date());
        if (StringUtil.isNotEmpty(bizIWarehouseReceiptHeadParam.getWarehouseEntryNumber())){
            head.setWarehouseEntryNumber(bizIWarehouseReceiptHeadParam.getWarehouseEntryNumber());
            //废弃的编码被重新使用后,拓展字段1，作为被重新使用的标记。
            mapper.updateByWarehouseEntryNumber(bizIWarehouseReceiptHeadParam.getWarehouseEntryNumber());
        }else {
            //查询今年最大的进仓编号
          String  maxWarehouseEntryNumber =mapper.selectMaxWarehouseEntryNumber("JCK(进)"+currentYear);
          if (StringUtil.isEmpty(maxWarehouseEntryNumber)){
              //如果为空重新生成
              head.setWarehouseEntryNumber("JCK(进)" + currentYear + "001");
          }else {
              //不为空，流水号加一
              // 提取最大流水号并加 1
              int maxSerialNumber = Integer.parseInt(maxWarehouseEntryNumber.substring(8));
              String newSerialNumber = String.format("%03d", maxSerialNumber + 1);
              head.setWarehouseEntryNumber("JCK(进)" + currentYear + newSerialNumber);
          }

        }
        //提货单号
        if (StringUtil.isNotEmpty(bizIWarehouseReceiptHeadParam.getLadingNumber())){
            head.setLadingNumber(bizIWarehouseReceiptHeadParam.getLadingNumber());
            //废弃的编码被重新使用后,拓展字段2，作为被重新使用的标记。
            mapper.updateByLadingNumber(bizIWarehouseReceiptHeadParam.getLadingNumber());


        }else {
            //查询今年最大的提货单号
            String  maxLadingNumber =mapper.selectMaxLadingNumber("JCK(出)"+currentYear);
            if (StringUtil.isEmpty(maxLadingNumber)){
                //如果为空重新生成
                head.setLadingNumber("JCK(出)" + currentYear + "001");
            }else {
                //不为空，流水号加一
                // 提取最大流水号并加 1
                int maxSerialNumber = Integer.parseInt(maxLadingNumber.substring(8));
                String newSerialNumber = String.format("%03d", maxSerialNumber + 1);
                head.setLadingNumber("JCK(出)" + currentYear + newSerialNumber);
            }
        }
        //进口发票号码
        head.setInvoiceNumber(bizIOrderHead.getImportInvoiceNo());
        //供应商
        head.setSupplier(bizIOrderHead.getPartyB());
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        //查询出全部的客商信息
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
//查询全部仓库参数
        Storehouse storehouse = new Storehouse();
        storehouse.setTradeCode(userInfo.getCompany());
        List<Storehouse> storehouseList = storehouseMapper.getList(storehouse);
        //出库默认个）海烟物流仓库
        head.setWarehouse(getStorehouseCodeByName(storehouseList,"（个）海烟物流仓库"));
        //提货单位 当供应商为"中烟英美烟草国际有限公司"时，提货单位默认：北京中烟三五品牌营销有限公司
        //其他供应商，提货单位默认：上海烟草贸易中心有限公司
        //根据供应商返回供应商的名称
        String name = getNameByCode(bizMerchants,head.getSupplier());


        if("中烟英美烟草国际有限公司".equals(name)){
            head.setLadingDepartment(getCodeByName(bizMerchants,"北京中烟三五品牌营销有限公司"));
        }else {//*********
            head.setLadingDepartment(getCodeByName(bizMerchants,"上海烟草贸易中心有限公司"));
        }


        //币制       //客户折扣率
        if (CollectionUtils.isNotEmpty(bizIPurchaseLists)){
            head.setCurr(bizIPurchaseLists.get(0).getCurr());
            //根据表体任一行商品名称，关联【进口计划】取折扣率
            head.setDiscountRate(mapper.selectDiscountRate(bizIPurchaseLists.get(0).getProductGrade(),bizIOrderHead.getPlanNo(),userInfo.getCompany()));
        }
        //卖出价汇率
        if(head.getCurr() != null){
            //根据币种，关联取【企业自定义参数-汇率】
            List<EnterpriseRate> select = enterpriseRateMapper.selectMessage(new EnterpriseRate() {{
                setTradeCode(userInfo.getCompany());
                setCurr(head.getCurr());
            }});
            if(CollectionUtils.isNotEmpty(select)){
                head.setSellingRate(select.get(0).getRate());
            }
        }


        head.setSid(UUID.randomUUID().toString());
        head.setInsertUser(userInfo.getUserNo());
        head.setInsertUserName(userInfo.getUserName());
        head.setTradeCode(userInfo.getCompany());
        head.setInsertTime(new Date());
        head.setStatus("0");
        head.setParentId(parentId);
        mapper.insert(head);
        //表体生成
        for (BizIPurchaseList purchaseList:bizIPurchaseLists) {
          BizIWarehouseReceiptList list=new BizIWarehouseReceiptList();
            list.setSid(purchaseList.getSid());
            list.setParentId(head.getSid());
            list.setTradeCode(userInfo.getCompany());
            list.setInsertTime(new Date());
            list.setInsertUser(userInfo.getUserNo());
            list.setInsertUserName(userInfo.getUserName());
    //商品名称
            list.setGoodsName(purchaseList.getProductGrade());
            //数量
            list.setQty(purchaseList.getQty());
            //单位
            list.setUnit(purchaseList.getUnit());
            //进口发票号码
            list.setInvoiceNumber(purchaseList.getInvoiceNo());
            //外币单价
            list.setForeignUnitPrice(purchaseList.getDecPrice());
            list.setRmbPrices(purchaseList.getDecPrice());
            //外币货价
            // 计算外币货价：单价 * 数量 * (1 - 折扣率/100)
            BigDecimal amount = purchaseList.getDecPrice().multiply(purchaseList.getQty());
            if (head.getDiscountRate() != null) {
                amount = amount.multiply(BigDecimal.ONE.subtract(head.getDiscountRate().divide(BigDecimal.valueOf(100))));
            }
            list.setForeignPrices(amount);
            listMapper.insert(list);
        }

        bizIPurchaseHead.setDataStatus("1");
        // 设置流向下一个节点状态
        bizIPurchaseHead.setIsNext("1");
        bizIPurchaseHead.setPurchaseConfirmationTime(new Date());
        bizIOrderHead.setPurchaseDataStatus("1");
        bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);
        bizIPurchaseHeadMapper.updateByPrimaryKey(bizIPurchaseHead);

        return bizIPurchaseHead;
    }

    /**
     * 根据仓库名称获取仓库code
     * @param storehouse
     * @param name
     * @return
     */
    public static String getStorehouseCodeByName(List<Storehouse> storehouse, String name) {
        if (storehouse == null || name == null) {
            return null;
        }

        return storehouse.stream()
                .filter(merchant -> name.equals(merchant.getStorehouseName()))
                .findFirst()
                .map(Storehouse::getParamCode)
                .orElse(null);
    }
    /**
     * 根据客商编码获取客商中文名称
     * @param bizMerchants 客商列表
     * @param merchantCode 要查找的客商编码
     * @return 对应的客商中文名称，如果未找到返回null
     */
    public static String getNameByCode(List<BizMerchant> bizMerchants, String merchantCode) {
        if (bizMerchants == null || merchantCode == null) {
            return "";
        }

        return bizMerchants.stream()
                .filter(merchant -> merchantCode.equals(merchant.getMerchantCode()))
                .findFirst()
                .map(BizMerchant::getMerchantNameCn)
                .orElse("");
    }
    /**
     * 根据客商中文名称获取客商编码
     * @param bizMerchants 客商列表
     * @param merchantNameCn 要查找的客商中文名称
     * @return 对应的客商编码，如果未找到返回null
     */
    public static String getCodeByName(List<BizMerchant> bizMerchants, String merchantNameCn) {
        if (bizMerchants == null || merchantNameCn == null) {
            return null;
        }

        return bizMerchants.stream()
                .filter(merchant -> merchantNameCn.equals(merchant.getMerchantNameCn()))
                .findFirst()
                .map(BizMerchant::getMerchantCode)
                .orElse(null);
    }
    public Boolean checkPrintNotice(BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) {
        Boolean result=true;
        BizIWarehouseReceiptHead head = mapper.getListBySid(param.getSid());

        //进货单信息
        BizIPurchaseHead bizIPurchaseHead=new BizIPurchaseHead();
        bizIPurchaseHead.setHeadId(head.getParentId());
        bizIPurchaseHead.setTradeCode(userInfo.getCompany());
        List<BizIPurchaseHead> bizIPurchaseHeads = bizIPurchaseHeadMapper.select(bizIPurchaseHead);
        if (CollectionUtils.isNotEmpty(bizIPurchaseHeads)){
            //进货单信息装箱表体
            BizIPurchaseListBox bizIPurchaseListBox=new BizIPurchaseListBox();
            bizIPurchaseListBox.setHeadId(bizIPurchaseHeads.get(0).getSid());
            bizIPurchaseListBox.setTradeCode(userInfo.getCompany());
            List<BizIPurchaseListBox> selectList = bizIPurchaseListBoxMapper.select(bizIPurchaseListBox);
            if (CollectionUtils.isEmpty(selectList)){
                return false;
            }
            //进货信息表体箱号不能为空
            for (BizIPurchaseListBox list:
                    selectList) {
                if (StringUtils.isEmpty(list.getBoxNo())){
                    return false;
                }
            }
        }else {
            result=false;
        }

        return result;
    }

    public String printNotic(BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) throws Exception{

        String exportFileName="" ;
        List<String> files=new ArrayList<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String templateName = "进仓通知.xlsx";
        String fileName = UUID.randomUUID().toString() + ".xlsx";
        List<BizIWarehouseReceiptHead> heads=new ArrayList<>();
        List<BizIWarehouseReceiptList> lists=new ArrayList<>();
        if (StringUtil.isEmpty(param.getSid())){
            throw new ErrorException(400, "入库回单表头不能为空");
        }
        BizIWarehouseReceiptHead head = mapper.getListBySid(param.getSid());
        if (null!=head.getEntryDate()){
            // 1. 将Date格式化为字符串
            String formattedDate = formatter.format(head.getEntryDate());
            head.setEntryDateString(formattedDate);

        }
        BizIWarehouseReceiptList listParam=new BizIWarehouseReceiptList();
        listParam.setParentId(param.getSid());
        listParam.setTradeCode(userInfo.getCompany());
        lists = listMapper.getPrintOfLadingList(listParam);
        for (BizIWarehouseReceiptList list : lists) {
            list.setQtyStr(NumberFormatterUtils.formatNumber(list.getQty()));
        }
        //计算表体的数据汇总
        head.setSumQty(lists.stream()
                .map(BizIWarehouseReceiptList::getQty) //
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        head.setSumQtyStr(NumberFormatterUtils.formatNumber(head.getSumQty()));
        heads.add(head);
        //供应商转换成name
        //查询出全部的客商信息
        BizMerchant bizMerchant = new BizMerchant();
        bizMerchant.setTradeCode(userInfo.getCompany());
        List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);
        head.setSupplier(getNameByCode(bizMerchants,head.getSupplier()));
        //备注
        if (!StringUtils.isEmpty(head.getOrderNo())){
            head.setOrderNo("订单号"+head.getOrderNo());
        }

        if (lists.size() > 0) {
            exportFileName = exportService.export(heads,lists, fileName, templateName);
           files.add(exportFileName);
        }
        //进货单信息
        BizIPurchaseHead bizIPurchaseHead=new BizIPurchaseHead();
        bizIPurchaseHead.setHeadId(head.getParentId());
        bizIPurchaseHead.setTradeCode(userInfo.getCompany());
        List<BizIPurchaseHead> bizIPurchaseHeads = bizIPurchaseHeadMapper.select(bizIPurchaseHead);
        if (null!=bizIPurchaseHeads.get(0)){
            //进货单信息装箱表体
            BizIPurchaseListBox bizIPurchaseListBox=new BizIPurchaseListBox();
            bizIPurchaseListBox.setHeadId(bizIPurchaseHeads.get(0).getSid());
            bizIPurchaseListBox.setTradeCode(userInfo.getCompany());
            List<BizIPurchaseListBox> selectList = bizIPurchaseListBoxMapper.select(bizIPurchaseListBox);
            //循环设置单位,默认件
            for (BizIPurchaseListBox list:
            selectList) {
                if (StringUtils.isEmpty(list.getBoxNo())){
                    throw new RuntimeException("进货信息表体箱号不能为空");
                }
                list.setUnit("件");
            }

            // 按boxNo分组
            Map<String, List<BizIPurchaseListBox>> groupedByBoxNo = selectList.stream()
                    .collect(Collectors.groupingBy(BizIPurchaseListBox::getBoxNo));

            // 获取所有的entry，以便判断是否是最后一个
            List<Map.Entry<String, List<BizIPurchaseListBox>>> entries = new ArrayList<>(groupedByBoxNo.entrySet());

            for (int i = 0; i < entries.size(); i++) {
                Map.Entry<String, List<BizIPurchaseListBox>> entry = entries.get(i);
                BizIPurchaseHead purchaseHead = bizIPurchaseHeads.get(0);
                //设置进货信息表体-箱号
                purchaseHead.setExtend1(entry.getKey());

                if (null!=head.getInsertTime()){
                    // 1. 将Date格式化为字符串
                    String formattedDate = formatter.format(purchaseHead.getInsertTime());
                    purchaseHead.setExtend2(formattedDate);
                }
                List<BizIPurchaseListBox> value = entry.getValue();
                for (BizIPurchaseListBox box : value) {
                    box.setQuantityStr(NumberFormatterUtils.formatNumber(box.getQuantity()));
                }
                //计算表体的数据汇总
                purchaseHead.setSumQty(value.stream()
                        .map(BizIPurchaseListBox::getQuantity) //
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                purchaseHead.setSumQtyStr(NumberFormatterUtils.formatNumber(purchaseHead.getSumQty()));
                List<BizIPurchaseHead> newBizIPurchaseHeads=new ArrayList<>();
                newBizIPurchaseHeads.add(purchaseHead);

                // 判断是否是最后一个元素，如果是最后一个则使用不同的模板
                if (i == entries.size() - 1) {
                    templateName = "进仓通知装箱.xlsx"; // 最后一个使用不同的模板
                } else {
                    templateName = "进仓通知装箱NOReview.xlsx"; // 其他使用原来的模板
                }

                fileName = UUID.randomUUID().toString() + ".xlsx";
                exportFileName = exportService.export(newBizIPurchaseHeads,value, fileName, templateName);
                files.add(exportFileName);
            }
        }
        ExcelMerger excelMerger=new ExcelMerger();
        return  excelMerger.appendToFirstFile(files, true);

    }

    public String printOfLading(BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String templateName = "提货单.xlsx";

        String fileName = UUID.randomUUID().toString() + ".xlsx";
        List<BizIWarehouseReceiptHead> heads=new ArrayList<>();
        List<BizIWarehouseReceiptList> lists=new ArrayList<>();
        if (StringUtil.isEmpty(param.getSid())){
            throw new ErrorException(400, "入库回单表头不能为空");
        }
        BizIWarehouseReceiptHead head = mapper.getListBySid(param.getSid());
        if (null!=head.getOutdate()){
            // 1. 将Date格式化为字符串
            String formattedDate = formatter.format(head.getOutdate());
            head.setOutdateString(formattedDate);

        }
        if (null!=head.getInsertTime()){
            // 1. 将Date格式化为字符串
            String formattedDate = formatter.format(head.getInsertTime());
            head.setInsertTimeString(formattedDate);

        }

        BizIWarehouseReceiptList listParam=new BizIWarehouseReceiptList();
        listParam.setParentId(param.getSid());
        listParam.setTradeCode(userInfo.getCompany());
        lists = listMapper.getPrintOfLadingList(listParam);
        for (BizIWarehouseReceiptList list : lists) {
            list.setQtyStr(NumberFormatterUtils.formatNumber(list.getQty()));
        }
        //计算表体的数据汇总
        head.setSumQty(lists.stream()
                .map(BizIWarehouseReceiptList::getQty) //
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        heads.add(head);
        head.setSumQtyStr(NumberFormatterUtils.formatNumber(head.getSumQty()));

        String exportFileName="" ;
        if (lists.size() > 0) {
            exportFileName = exportService.export(heads,lists, fileName, templateName);
        }
        return exportFileName;

    }

    /**
     * 打印入库回单（支持PDF和XLSX格式）
     * @param sid 主键ID
     * @param fileType 文件类型（xlsx或pdf，默认为pdf）
     * @param userInfo 用户信息
     * @return 响应实体
     * @throws Exception 可能发生的异常
     */
    public ResponseEntity print(String sid, String fileType, UserInfoToken userInfo) throws Exception {
        BizIWarehouseReceiptHeadToFile head = mapper.getHeadForFile(sid);
        List<BizIWarehouseReceiptList> list = listMapper.getListForFile(sid);
        String templateName = "biz_warehouse_receipt.xlsx";
        String finalOutName = "";
        String finalExportFileName = "";

        if (head != null && CollectionUtils.isNotEmpty(list)) {

            head.setRateStr("汇率: " +NumberFormatterUtils.formatNumber(head.getRate()));
            head.setDiscountRateStr("折扣率: " +NumberFormatterUtils.formatNumber(head.getDiscountRate())+"%");
            head.setSellingRateStr("卖出价: " +NumberFormatterUtils.formatNumber(head.getSellingRate()));

            String warehouseReceiptNumber = head.getWarehouseReceiptNumber() == null ? "" : head.getWarehouseReceiptNumber();

            // 查询出全部的客商信息
            BizMerchant bizMerchant = new BizMerchant();
            bizMerchant.setTradeCode(userInfo.getCompany());
            List<BizMerchant> bizMerchants = bizMerchantMapper.getList(bizMerchant);

            // 供应商转换成name
            head.setSupplier("来货公司：" + (StringUtils.isEmpty(getNameByCode(bizMerchants, head.getSupplier()))?"":getNameByCode(bizMerchants, head.getSupplier())));

            // 处理表体数据
            for (BizIWarehouseReceiptList item : list) {
                item.setSerialNo("NO." + warehouseReceiptNumber + "-" + item.getSerialNo());
                item.setInvoiceNumber("发票号：" + (item.getInvoiceNumber() == null ? "" : item.getInvoiceNumber()));
                item.setConvertedTotalAmount(" 大       写： " + RMBConverterUtil.convertToRMBTraditional(item.getTotalAmount().toString()));
                item.setQtyStr(NumberFormatterUtils.formatNumber(item.getQty()));
                item.setForeignUnitPriceStr(NumberFormatterUtils.formatNumber(item.getForeignUnitPrice()));
                item.setRmbUnitPriceStr(NumberFormatterUtils.formatNumber(item.getRmbUnitPrice()));
                item.setTaxAmountStr(NumberFormatterUtils.formatNumber(item.getTaxAmount()));
                item.setCostAmountStr(NumberFormatterUtils.formatNumber(item.getCostAmount()));
                item.setTotalAmountStr(NumberFormatterUtils.formatNumber(item.getTotalAmount()));
                item.setForeignPricesStr(NumberFormatterUtils.formatNumber(item.getForeignPrices()));
                item.setRmbPricesStr(NumberFormatterUtils.formatNumber(item.getRmbPrices()));
                item.setTariffStr(NumberFormatterUtils.formatNumber(item.getTariff()));
                item.setConsumptionTaxStr(NumberFormatterUtils.formatNumber(item.getConsumptionTax()));
                item.setValueAddedTaxStr(NumberFormatterUtils.formatNumber(item.getValueAddedTax()));
            }

            // 根据文件类型选择导出方式
            if ("xlsx".equalsIgnoreCase(fileType)) {
                // Excel导出 - 多Sheet方式
                finalOutName = xdoi18n.XdoI18nUtil.t("入库回单") + warehouseReceiptNumber + ".xlsx";

                // 创建临时文件列表，存储每个表体对应的Excel文件
                List<String> excelFiles = new ArrayList<>();

                // 为每个表体项创建单独的Excel文件
                for (int i = 0; i < list.size(); i++) {
                    BizIWarehouseReceiptList item = list.get(i);
                    String sheetName = "NO." + warehouseReceiptNumber + "-" + item.getSerialNo();
                    String tempFileName = UUID.randomUUID() + ".xlsx";

                    // 导出单个表体项到Excel
                    String exportFileName = exportService.export(
                        Arrays.asList(head),
                        Arrays.asList(item),
                        tempFileName,
                        templateName
                    );

                    excelFiles.add(exportFileName);
                }

                // 合并所有Excel文件到一个文件中
                finalExportFileName = UUID.randomUUID() + ".xlsx";
                mergeExcelFiles(excelFiles, finalExportFileName, warehouseReceiptNumber, list);

                HttpHeaders h = new HttpHeaders();
                finalOutName = URLEncoder.encode(finalOutName, CommonVariable.UTF8);
                finalOutName = finalOutName.replaceAll("\\+", "%20");
                h.add(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                        + new String(finalOutName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
                return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(finalExportFileName)), h, HttpStatus.OK);
            } else {
                // PDF导出（原有逻辑）
                List<String> pdfFiles = new ArrayList<>();
                finalOutName = xdoi18n.XdoI18nUtil.t("入库回单") + warehouseReceiptNumber + ".pdf";

                for (BizIWarehouseReceiptList item : list) {
                    String fileName = UUID.randomUUID() + ".pdf";
                    String exportFileName = exportService.export(Arrays.asList(head), Arrays.asList(item), fileName, templateName);
                    pdfFiles.add(exportFileName);
                }

                // 合并所有PDF文件
                finalExportFileName = UUID.randomUUID() + ".pdf";
                String title = "入库回单 - " + warehouseReceiptNumber; // 设置文档标题
                mergePDFFiles(pdfFiles, finalExportFileName, title);

                HttpHeaders h = new HttpHeaders();
                finalOutName = URLEncoder.encode(finalOutName, CommonVariable.UTF8);
                finalOutName = finalOutName.replaceAll("\\+", "%20");
                h.add(HttpHeaders.CONTENT_TYPE, "application/pdf");
                h.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="
                        + new String(finalOutName.getBytes(CommonVariable.UTF8), "ISO8859-1"));
                return new ResponseEntity<byte[]>(FileUtils.readFileToByteArray(new File(finalExportFileName)), h, HttpStatus.OK);
            }
        }

        // 如果没有有效数据，返回空响应
        return ResponseEntity.noContent().build();
    }

    // 为兼容原有接口，保留无fileType参数的方法
    public ResponseEntity print(String sid, UserInfoToken userInfo) throws Exception {
        return print(sid, "pdf", userInfo); // 默认导出PDF格式
    }

    /**
     * 合并多个PDF文件为一个文件
     * @param inputPdfList 输入PDF文件路径列表
     * @param outputPdfPath 输出PDF文件路径
     * @param title 文档标题
     * @throws Exception 如果合并过程中发生错误
     */
    private void mergePDFFiles(List<String> inputPdfList, String outputPdfPath, String title) throws Exception {
        // 创建PDFMergerUtility对象
        PDFMergerUtility pdfMerger = new PDFMergerUtility();
        pdfMerger.setDestinationFileName(outputPdfPath);

        for (String pdfFile : inputPdfList) {
            pdfMerger.addSource(new File(pdfFile));
        }

        pdfMerger.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());

        // 修改输出PDF的元数据
        try (PDDocument document = PDDocument.load(new File(outputPdfPath))) {
            PDDocumentInformation info = document.getDocumentInformation();
            info.setTitle(title);
            info.setAuthor("海南省烟草进出口有限公司");  // 设置作者
            info.setCreator("进口卷烟管理系统");         // 设置创建工具
            info.setProducer("海南省烟草进出口公司进口卷烟管理系统");  // 设置生产者
            info.setKeywords("入库回单");              // 设置关键词
            document.save(outputPdfPath);
        }

        // 合并完成后删除临时文件
        for (String pdfFile : inputPdfList) {
            new File(pdfFile).delete();
        }
    }

    /**
     * 合并多个Excel文件为一个文件，每个Excel作为新文件的一个Sheet
     * @param inputExcelList 输入Excel文件路径列表
     * @param outputExcelPath 输出Excel文件路径
     * @param receiptNumber 入库回单编号（用于Sheet命名）
     * @param list 表体数据，用于获取商品名称作为Sheet名
     * @throws Exception 如果合并过程中发生错误
     */
    private void mergeExcelFiles(List<String> inputExcelList, String outputExcelPath, String receiptNumber,
                                 List<BizIWarehouseReceiptList> list) throws Exception {
        // 创建新的工作簿
        Workbook workbook = new XSSFWorkbook();

        for (int i = 0; i < inputExcelList.size(); i++) {
            String excelFilePath = inputExcelList.get(i);

            // 打开源Excel文件
            FileInputStream fis = new FileInputStream(new File(excelFilePath));
            Workbook sourceWorkbook = new XSSFWorkbook(fis);

            // 获取第一个Sheet（假设只有一个Sheet）
            Sheet sourceSheet = sourceWorkbook.getSheetAt(0);

            // 使用表体的商品名称作为Sheet名称
            String sheetName = list.get(i).getGoodsName();
            // 如果商品名称为空或过长，使用默认名称
            if (StringUtils.isEmpty(sheetName) || sheetName.length() > 31) {
                sheetName = "项目" + (i + 1);
            }

            // 检查工作簿中是否已存在同名Sheet，如果存在则添加序号
            int suffix = 1;
            String originalSheetName = sheetName;
            while (workbook.getSheet(sheetName) != null) {
                // 如果已存在同名Sheet，添加序号
                suffix++;
                // 确保名称不超过31个字符（Excel的限制）
                if (originalSheetName.length() > 27) {
                    originalSheetName = originalSheetName.substring(0, 27);
                }
                sheetName = originalSheetName + "(" + suffix + ")";
            }

            Sheet targetSheet = workbook.createSheet(sheetName);

            // 复制所有行和单元格
            for (int rowNum = 0; rowNum <= sourceSheet.getLastRowNum(); rowNum++) {
                Row sourceRow = sourceSheet.getRow(rowNum);
                if (sourceRow != null) {
                    Row targetRow = targetSheet.createRow(rowNum);

                    // 复制行高
                    targetRow.setHeight(sourceRow.getHeight());

                    // 复制所有单元格
                    for (int colNum = 0; colNum < sourceRow.getLastCellNum(); colNum++) {
                        Cell sourceCell = sourceRow.getCell(colNum);
                        if (sourceCell != null) {
                            Cell targetCell = targetRow.createCell(colNum);

                            // 复制单元格样式
                            CellStyle newStyle = workbook.createCellStyle();
                            newStyle.cloneStyleFrom(sourceCell.getCellStyle());
                            targetCell.setCellStyle(newStyle);

                            // 复制单元格内容
                            switch (sourceCell.getCellType()) {
                                case STRING:
                                    targetCell.setCellValue(sourceCell.getStringCellValue());
                                    break;
                                case NUMERIC:
                                    if (DateUtil.isCellDateFormatted(sourceCell)) {
                                        targetCell.setCellValue(sourceCell.getDateCellValue());
                                    } else {
                                        targetCell.setCellValue(sourceCell.getNumericCellValue());
                                    }
                                    break;
                                case BOOLEAN:
                                    targetCell.setCellValue(sourceCell.getBooleanCellValue());
                                    break;
                                case FORMULA:
                                    targetCell.setCellValue(sourceCell.getCellFormula());
                                    break;
                                case BLANK:
                                    // 空单元格不需要处理
                                    break;
                                default:
                                    // 其他类型暂不处理
                                    break;
                            }
                        }
                    }
                }
            }

            // 设置第一行和第二行为合并居中
            // 第一行合并 A-H
            CellRangeAddress firstRowRegion = new CellRangeAddress(0, 0, 0, 7);
            targetSheet.addMergedRegion(firstRowRegion);
            // 第二行合并 A-H
            CellRangeAddress secondRowRegion = new CellRangeAddress(1, 1, 0, 7);
            targetSheet.addMergedRegion(secondRowRegion);
            // 第三行 F-H 合并并右对齐
            CellRangeAddress g3h3Region = new CellRangeAddress(2, 2, 5, 7);
            targetSheet.addMergedRegion(g3h3Region);

            // 设置合并单元格的样式为居中
            if (targetSheet.getRow(0) != null && targetSheet.getRow(0).getCell(0) != null) {
                CellStyle centerStyle = workbook.createCellStyle();
                centerStyle.cloneStyleFrom(targetSheet.getRow(0).getCell(0).getCellStyle());
                centerStyle.setAlignment(HorizontalAlignment.CENTER);
                targetSheet.getRow(0).getCell(0).setCellStyle(centerStyle);
            }

            if (targetSheet.getRow(1) != null && targetSheet.getRow(1).getCell(0) != null) {
                CellStyle centerStyle = workbook.createCellStyle();
                centerStyle.cloneStyleFrom(targetSheet.getRow(1).getCell(0).getCellStyle());
                centerStyle.setAlignment(HorizontalAlignment.CENTER);
                targetSheet.getRow(1).getCell(0).setCellStyle(centerStyle);
            }

            // 设置G3-H3单元格右对齐
            if (targetSheet.getRow(2) != null && targetSheet.getRow(2).getCell(6) != null) {
                CellStyle rightAlignStyle = workbook.createCellStyle();
                rightAlignStyle.cloneStyleFrom(targetSheet.getRow(2).getCell(6).getCellStyle());
                rightAlignStyle.setAlignment(HorizontalAlignment.RIGHT);
                targetSheet.getRow(2).getCell(6).setCellStyle(rightAlignStyle);
            }

            // 设置A12-C12、A13-C13、A14-C14单元格合并和文本左对齐
            for (int rowNum = 11; rowNum <= 13; rowNum++) { // Excel行是从0开始的，所以12行是11索引
                CellRangeAddress mergeRegion = new CellRangeAddress(rowNum, rowNum, 0, 2);
                targetSheet.addMergedRegion(mergeRegion);

                // 设置合并后的单元格为左对齐
                if (targetSheet.getRow(rowNum) != null && targetSheet.getRow(rowNum).getCell(0) != null) {
                    CellStyle leftAlignStyle = workbook.createCellStyle();
                    leftAlignStyle.cloneStyleFrom(targetSheet.getRow(rowNum).getCell(0).getCellStyle());
                    leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);
                    targetSheet.getRow(rowNum).getCell(0).setCellStyle(leftAlignStyle);
                }
            }

            // 设置E12-H12、E13-H13、E14-H14单元格合并和文本左对齐
            for (int rowNum = 11; rowNum <= 13; rowNum++) { // Excel行是从0开始的，所以12行是11索引
                CellRangeAddress mergeRegion = new CellRangeAddress(rowNum, rowNum, 4, 7); // E列到H列对应索引4-7
                targetSheet.addMergedRegion(mergeRegion);

                // 设置合并后的单元格为左对齐
                if (targetSheet.getRow(rowNum) != null && targetSheet.getRow(rowNum).getCell(4) != null) {
                    CellStyle leftAlignStyle = workbook.createCellStyle();
                    leftAlignStyle.cloneStyleFrom(targetSheet.getRow(rowNum).getCell(4).getCellStyle());
                    leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);
                    targetSheet.getRow(rowNum).getCell(4).setCellStyle(leftAlignStyle);
                }
            }

            // 设置A4-B4、C4-E4、F4-G4合并并居中对齐
            // A4-B4合并
            CellRangeAddress a4b4Region = new CellRangeAddress(3, 3, 0, 1); // 第4行(索引3)，A-B列(索引0-1)
            targetSheet.addMergedRegion(a4b4Region);
            // C4-E4合并
            CellRangeAddress c4e4Region = new CellRangeAddress(3, 3, 2, 4); // 第4行(索引3)，C-E列(索引2-4)
            targetSheet.addMergedRegion(c4e4Region);
            // F4-G4合并
            CellRangeAddress f4g4Region = new CellRangeAddress(3, 3, 5, 6); // 第4行(索引3)，F-G列(索引5-6)
            targetSheet.addMergedRegion(f4g4Region);

            // 设置第4行合并单元格居中对齐
            if (targetSheet.getRow(3) != null) {
                // A4-B4居中
                if (targetSheet.getRow(3).getCell(0) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(3).getCell(0).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(3).getCell(0).setCellStyle(centerStyle);
                }
                // C4-E4居中
                if (targetSheet.getRow(3).getCell(2) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(3).getCell(2).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(3).getCell(2).setCellStyle(centerStyle);
                }
                // F4-G4居中
                if (targetSheet.getRow(3).getCell(5) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(3).getCell(5).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(3).getCell(5).setCellStyle(centerStyle);
                }
            }

            // 设置A5-A6、B5-B6、C5-E5合并
            // A5-A6合并
            CellRangeAddress a5a6Region = new CellRangeAddress(4, 5, 0, 0); // 第5-6行(索引4-5)，A列(索引0)
            targetSheet.addMergedRegion(a5a6Region);
            // B5-B6合并
            CellRangeAddress b5b6Region = new CellRangeAddress(4, 5, 1, 1); // 第5-6行(索引4-5)，B列(索引1)
            targetSheet.addMergedRegion(b5b6Region);
            // C5-E5合并
            CellRangeAddress c5e5Region = new CellRangeAddress(4, 4, 2, 4); // 第5行(索引4)，C-E列(索引2-4)
            targetSheet.addMergedRegion(c5e5Region);
            // D6-E6合并
            CellRangeAddress d6e6Region = new CellRangeAddress(5, 5, 3, 4); // 第6行(索引5)，D-E列(索引3-4)
            targetSheet.addMergedRegion(d6e6Region);
            // F5-F6合并
            CellRangeAddress f5f6Region = new CellRangeAddress(4, 5, 5, 5); // 第5-6行(索引4-5)，F列(索引5)
            targetSheet.addMergedRegion(f5f6Region);
            // G5-G6合并
            CellRangeAddress g5g6Region = new CellRangeAddress(4, 5, 6, 6); // 第5-6行(索引4-5)，G列(索引6)
            targetSheet.addMergedRegion(g5g6Region);
            // H5-H6合并
            CellRangeAddress h5h6Region = new CellRangeAddress(4, 5, 7, 7); // 第5-6行(索引4-5)，H列(索引7)
            targetSheet.addMergedRegion(h5h6Region);
            // A7-A11、B7-B11、C7-C11、D7-E11合并
            // A7-A11合并
            CellRangeAddress a7a11Region = new CellRangeAddress(6, 10, 0, 0); // 第7-11行(索引6-10)，A列(索引0)
            targetSheet.addMergedRegion(a7a11Region);
            // B7-B11合并
            CellRangeAddress b7b11Region = new CellRangeAddress(6, 10, 1, 1); // 第7-11行(索引6-10)，B列(索引1)
            targetSheet.addMergedRegion(b7b11Region);
            // C7-C11合并
            CellRangeAddress c7c11Region = new CellRangeAddress(6, 10, 2, 2); // 第7-11行(索引6-10)，C列(索引2)
            targetSheet.addMergedRegion(c7c11Region);
            // D7-E11合并
            CellRangeAddress d7e11Region = new CellRangeAddress(6, 10, 3, 4); // 第7-11行(索引6-10)，D-E列(索引3-4)
            targetSheet.addMergedRegion(d7e11Region);

            // 设置合并单元格居中对齐
            if (targetSheet.getRow(4) != null) {
                // A5-A6居中
                if (targetSheet.getRow(4).getCell(0) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(4).getCell(0).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(4).getCell(0).setCellStyle(centerStyle);
                }
                // B5-B6居中
                if (targetSheet.getRow(4).getCell(1) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(4).getCell(1).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(4).getCell(1).setCellStyle(centerStyle);
                }
                // C5-E5居中
                if (targetSheet.getRow(4).getCell(2) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(4).getCell(2).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(4).getCell(2).setCellStyle(centerStyle);
                }
                // F5-F6居中
                if (targetSheet.getRow(4).getCell(5) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(4).getCell(5).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(4).getCell(5).setCellStyle(centerStyle);
                }
                // G5-G6居中
                if (targetSheet.getRow(4).getCell(6) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(4).getCell(6).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(4).getCell(6).setCellStyle(centerStyle);
                }
                // H5-H6居中
                if (targetSheet.getRow(4).getCell(7) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(4).getCell(7).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(4).getCell(7).setCellStyle(centerStyle);
                }
            }

            // D6-E6居中
            if (targetSheet.getRow(5) != null && targetSheet.getRow(5).getCell(3) != null) {
                CellStyle centerStyle = workbook.createCellStyle();
                centerStyle.cloneStyleFrom(targetSheet.getRow(5).getCell(3).getCellStyle());
                centerStyle.setAlignment(HorizontalAlignment.CENTER);
                targetSheet.getRow(5).getCell(3).setCellStyle(centerStyle);
            }

            // 设置A7-A11、B7-B11、C7-C11、D7-E11居中对齐
            if (targetSheet.getRow(6) != null) {
                // A7-A11居中
                if (targetSheet.getRow(6).getCell(0) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(6).getCell(0).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(6).getCell(0).setCellStyle(centerStyle);
                }
                // B7-B11居中
                if (targetSheet.getRow(6).getCell(1) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(6).getCell(1).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(6).getCell(1).setCellStyle(centerStyle);
                }
                // C7-C11居中
                if (targetSheet.getRow(6).getCell(2) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(6).getCell(2).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(6).getCell(2).setCellStyle(centerStyle);
                }
                // D7-E11居中
                if (targetSheet.getRow(6).getCell(3) != null) {
                    CellStyle centerStyle = workbook.createCellStyle();
                    centerStyle.cloneStyleFrom(targetSheet.getRow(6).getCell(3).getCellStyle());
                    centerStyle.setAlignment(HorizontalAlignment.CENTER);
                    targetSheet.getRow(6).getCell(3).setCellStyle(centerStyle);
                }
            }

            // 设置A15-H15合并并左对齐
            CellRangeAddress a15h15Region = new CellRangeAddress(14, 14, 0, 7); // 第15行(索引14)，A-H列(索引0-7)
            targetSheet.addMergedRegion(a15h15Region);

            // 设置A15-H15左对齐
            if (targetSheet.getRow(14) != null && targetSheet.getRow(14).getCell(0) != null) {
                CellStyle leftAlignStyle = workbook.createCellStyle();
                leftAlignStyle.cloneStyleFrom(targetSheet.getRow(14).getCell(0).getCellStyle());
                leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);
                targetSheet.getRow(14).getCell(0).setCellStyle(leftAlignStyle);
            }

            // 设置E16-F16合并并居中对齐
            CellRangeAddress e16f16Region = new CellRangeAddress(15, 15, 4, 5); // 第16行(索引15)，E-F列(索引4-5)
            targetSheet.addMergedRegion(e16f16Region);

            // 设置E16-F16居中对齐
            if (targetSheet.getRow(15) != null && targetSheet.getRow(15).getCell(4) != null) {
                CellStyle centerStyle = workbook.createCellStyle();
                centerStyle.cloneStyleFrom(targetSheet.getRow(15).getCell(4).getCellStyle());
                centerStyle.setAlignment(HorizontalAlignment.CENTER);
                targetSheet.getRow(15).getCell(4).setCellStyle(centerStyle);
            }

            // 设置列宽
            for (int colNum = 0; colNum < 8; colNum++) { // 只处理A-H列
                targetSheet.setColumnWidth(colNum, sourceSheet.getColumnWidth(colNum));
            }

            // 设置打印区域为A-H列
            workbook.setPrintArea(workbook.getSheetIndex(targetSheet), 0, 7, 0, targetSheet.getLastRowNum());

            // 设置页面布局属性
            targetSheet.setFitToPage(true);
            PrintSetup printSetup = targetSheet.getPrintSetup();
            printSetup.setFitWidth((short) 1);
            printSetup.setFitHeight((short) 0);

            // 关闭源工作簿
            sourceWorkbook.close();
            fis.close();
        }

        // 保存合并后的Excel
        FileOutputStream fos = new FileOutputStream(outputExcelPath);
        workbook.write(fos);
        fos.close();
        workbook.close();

        // 删除临时文件
        for (String excelFile : inputExcelList) {
            new File(excelFile).delete();
        }
    }

    //销售+回单数据初始化
    public void initSaleReturnData(String sid, UserInfoToken userInfo) {
        //删除已有销售信息+出库回单
        deleteMessage(sid,userInfo);
        //初始化销售信息和出库信息
        String sellSid = UUID.randomUUID().toString();
        String receiptSid = UUID.randomUUID().toString();
        bizISellHeadMapper.insertHeadList(sellSid,sid,userInfo);
        bizIReceiptHeadMapper.insertHeadList(receiptSid,sid,userInfo);
    }

    public void deleteMessage(String sid, UserInfoToken userInfo) {

        List<BizISellHead> select = bizISellHeadMapper.select(new BizISellHead() {{
            setHeadId(sid);
        }});
        if (!select.isEmpty()) {
            BizISellHead bizISellHead = select.get(0);
            bizISellHeadMapper.deleteByPrimaryKey(bizISellHead.getSid());
            bizISellListMapper.delete(new BizISellList(){{
                setHeadId(bizISellHead.getSid());
            }});
        }
        List<BizIReceiptHead> select1 = bizIReceiptHeadMapper.select(new BizIReceiptHead() {{
            setHeadId(sid);
        }});
        if (!select1.isEmpty()) {
            BizIReceiptHead bizIReceiptHead = select1.get(0);
            bizIReceiptHeadMapper.deleteByPrimaryKey(bizIReceiptHead.getSid());
            bizIReceiptListMapper.delete(new BizIReceiptList(){{
                setHeadId(bizIReceiptHead.getSid());
            }});
        }
    }
    public BizIWarehouseReceiptHeadDto onSure(BizIWarehouseReceiptHeadParam param, UserInfoToken userInfo) {
        String sid = param.getSid();
        BizIWarehouseReceiptHead head = mapper.selectByPrimaryKey(sid);
        if (!"1".equals(head.getExtend3())){
            throw new RuntimeException("未保存,不可以进行确定操作");
        }

        head.setStatus("1");
        head.setIsNext("1");
        mapper.updateByPrimaryKey(head);

        //订单表体状态更新
        BizIOrderHead bizIOrderHead = bizIOrderHeadMapper.selectByPrimaryKey(head.getParentId());
        bizIOrderHead.setInboundReceiptStatus("1");
        bizIOrderHeadMapper.updateByPrimaryKey(bizIOrderHead);
        initSaleReturnData(head.getParentId(),userInfo);
        List<BizIWarehouseReceiptList> list = listMapper.select(new BizIWarehouseReceiptList() {{
            setParentId(head.getSid());
        }});
        //触发对接用友系统接口
        sendYonyou(head,list,userInfo);
        return dtoMapper.toDto(head);
    }

    private void sendYonyou(BizIWarehouseReceiptHead head, List<BizIWarehouseReceiptList> list, UserInfoToken userInfo) {
        OBillJck oBillJck = headMessage(head, list, userInfo);
        List<OBillBJck> oBillBJcks = listMessage(head, list, userInfo);
        //存储信息
        try {
            Map<String, Object> stringObjectMap = thirdPartyDbService.convertToMap(oBillJck);
//            stringObjectMap.put("MQ_LSH","SEQ_USE_EXEM.NEXTVAL");
            thirdPartyDbService.insertData("O_BILL_JCK",stringObjectMap);
            List<Map<String, Object>> maps = thirdPartyDbService.convertListToMapList(oBillBJcks);
            thirdPartyDbService.batchInsertData("O_BILL_B_JCK",maps);
        } catch (IllegalAccessException e) {
            throw new RuntimeException("发生用友失败！");
        }
    }

    private OBillJck headMessage(BizIWarehouseReceiptHead head,List<BizIWarehouseReceiptList> list, UserInfoToken userInfo){
        OBillJck oBillJck = new OBillJck();
//--------字段名称----字段名------------取值
        //单据主键　	billid            接口程序赋值
        oBillJck.setBillid(head.getSid());
        //单据编号　	billcode          入库回单表头-入库回单编号
        oBillJck.setBillcode(head.getWarehouseReceiptNumber());
        //单据日期	billdate          入库回单表头-业务日期
        if(head.getBusinessDate() != null){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String formattedDate = sdf.format(head.getBusinessDate());
            oBillJck.setBilldate(formattedDate);
        }
        //业务人员	person           表头-制单人登录用户名
        oBillJck.setPerson(head.getInsertUser());
        //制单人员	maker            表头-制单人登录用户名
        oBillJck.setMaker(head.getInsertUser());
        //客商编码	cust              入库回单表头-供应商（取财务系统编码）
        oBillJck.setCust(head.getSupplier());
        //业务员名称	name_psndoc       制单人用户姓名
        oBillJck.setNamePsndoc(head.getInsertUserName());
        //操作员名称	name_operator     制单人用户姓名
        oBillJck.setNameOperator(head.getInsertUserName());
        //客商名称	name_cumandoc     入库回单表头-供应商（中文名称）
        if(StringUtil.isNotEmpty(head.getSupplier())){
            BizMerchant bizMerchant = new BizMerchant();
            bizMerchant.setTradeCode(userInfo.getCompany());
            bizMerchant.setMerchantCode(head.getSupplier());
            //查询出全部的客商信息
            List<BizMerchant> select1 = bizMerchantMapper.select(bizMerchant);
            oBillJck.setNameCumandoc(select1.get(0).getMerchantNameCn());
        }
        //系统时间	ts                接口数据发送时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDate = sdf.format(new Date());
        oBillJck.setTs(formattedDate);
        //订单号	    hth               入库回单表头-订单号
        oBillJck.setHth(head.getOrderNo());
        //仓库代码	storage           入库回单表头-仓库（代码）
        oBillJck.setStorage(head.getWarehouse());
        //仓库名称	name_storage      入库回单表头-仓库名称
        if(StringUtil.isNotEmpty(head.getWarehouse())){
            List<Storehouse> select = storehouseMapper.select(new Storehouse() {{
                setTradeCode(userInfo.getCompany());
                setParamCode(head.getWarehouse());
            }});
            oBillJck.setNameStorage(select.get(0).getStorehouseName());
        }
        //总费用	    totalmoney        汇总-表体人民币货价
        if(CollectionUtils.isNotEmpty(list)){
            oBillJck.setTotalmoney(list.stream().map(BizIWarehouseReceiptList::getRmbPrices).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        //发票号	    billno            入库回单表体-进口发票号码
            oBillJck.setBillno(list.stream().map(BizIWarehouseReceiptList::getInvoiceNumber).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.joining(",")));
        //表体行数	row_count         接口表赋值：表体数据的行数
            oBillJck.setRowCount(list.size());
        }
//--------字段名称-----字段名------------默认值
        //单据类型	  rd_type          RR
        oBillJck.setRdType("RR");
        //库存组织	  rdcenter         JCK01
        oBillJck.setRdcenter("JCK01");
        //公司名称	  company          中国烟草上海进出口有限责任公司
        oBillJck.setCompany("中国烟草上海进出口有限责任公司");
        //部门编码	  deptdoc          04
        oBillJck.setDeptdoc("04");
        //库存组织名称  name_calbody     个别计价
        oBillJck.setNameCalbody("个别计价");
        //部门名称	  name_deptdoc     业务一部
        oBillJck.setNameDeptdoc("业务一部");
        //            mq_op            i
        oBillJck.setMqOp("i");
        //            mq_st            0
        oBillJck.setMqSt("0");
        //            mq_count         1
        oBillJck.setMqCount(1);
        //            pk_corp          1022
        oBillJck.setPkCorp("1022");
        //            dr               0
        oBillJck.setDr(0);
        //币种编码	  currtypecode     CNY
        oBillJck.setCurrtypecode("CNY");
        //币种名称	  currtypename     人民币
        oBillJck.setCurrtypename("人民币");
        return oBillJck;
    }
    private List<OBillBJck> listMessage(BizIWarehouseReceiptHead head,List<BizIWarehouseReceiptList> list, UserInfoToken userInfo){
        List<OBillBJck> oBillBJcks = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(list)){
            for (BizIWarehouseReceiptList bizIWarehouseReceiptList : list) {
                OBillBJck oBillBJck = new OBillBJck();
                //--字段名称---字段名-----------取值
                //关联主表外键  billid          接口程序赋值
                oBillBJck.setBillid(bizIWarehouseReceiptList.getParentId());
                //存货编码	  inventory       根据入库回单表体-商品名称关联【物料信息】的条形码
                List<BizMaterialInformation> select = bizMaterialInformationMapper.select(new BizMaterialInformation() {{
                    setTradeCode(userInfo.getCompany());
                    setGname(bizIWarehouseReceiptList.getGoodsName());
                }});
                if(CollectionUtils.isNotEmpty(select)){
                    oBillBJck.setInventory(select.get(0).getBarCode());
                }
                //数量	      tnumber         入库回单表体-数量
                oBillBJck.setTnumber(bizIWarehouseReceiptList.getQty());
                //计量单位	  mainunit        关联取值用友的计量单位代码
                List<BaseInfoCustomerParams> selectCurr = baseInfoCustomerParamsMapper.select(new BaseInfoCustomerParams() {{
                    setCustomParamCode(head.getCurr());
                    setTradeCode(userInfo.getCompany());
                }});
                if(CollectionUtils.isNotEmpty(selectCurr)){
                    oBillBJck.setMainunit(selectCurr.get(0).getUnitYonyou());
                }
                //计量单位名称  name_mainunit   入库回单表体-单位（名称）
                oBillBJck.setNameMainunit(bizIWarehouseReceiptList.getUnit());
                //单价	      price           表体-人民币单价
                oBillBJck.setPrice(bizIWarehouseReceiptList.getRmbUnitPrice());
                //不含税金额	  notaxmoney      表体-成本金额小计
                oBillBJck.setNotaxmoney(bizIWarehouseReceiptList.getCostAmount());
                //金额	      totalmoney      表体-合计金额
                oBillBJck.setTotalmoney(bizIWarehouseReceiptList.getTotalAmount());
                //税额	      taxmoney        表体-税金金额小计
                oBillBJck.setTaxmoney(bizIWarehouseReceiptList.getTaxAmount());
                //货价	      hj              表体-人民币单价
                oBillBJck.setHj(bizIWarehouseReceiptList.getRmbUnitPrice());
                //关税	      gs              入库回单表体-关税
                oBillBJck.setGs(bizIWarehouseReceiptList.getTariff());
                //消费税	      xfs             入库回单表体-消费税
                oBillBJck.setXfs(bizIWarehouseReceiptList.getConsumptionTax());
                //存货名称	  name_invmandoc  入库回单表体-商品名称
                oBillBJck.setNameInvmandoc(bizIWarehouseReceiptList.getGoodsName());
                //系统时间	  ts              接口数据发送时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDate = sdf.format(new Date());
                oBillBJck.setTs(formattedDate);
                //客商编码	  shipcustname    入库回单表头-供应商（取财务系统编码）
                oBillBJck.setShipcustname(head.getSupplier());
                //订单号	      orderno         入库回单表头-订单号
                oBillBJck.setOrderno(head.getOrderNo());
                //发票号	      billno_bt       入库回单表体-进口发票号
                oBillBJck.setBillnoBt(bizIWarehouseReceiptList.getInvoiceNumber());
                //客商编码	  pk_shipcust     入库回单表头-供应商（取财务系统编码）
                if(StringUtil.isNotEmpty(head.getSupplier())){
                    BiClientInformation biClientInformation = new BiClientInformation();
                    biClientInformation.setTradeCode(userInfo.getCompany());
                    biClientInformation.setCustomerCode(head.getSupplier());
                    biClientInformation.setStatus("0");
                    //查询出全部的客商信息
                    List<BiClientInformation> selectSupplier = biClientInformationMapper.select(biClientInformation);
                    oBillBJck.setPkShipcust(selectSupplier.get(0).getCompanyName());
                }
                //--字段名称---字段名-----------默认值
                //海运费	      hyf             0.00
                oBillBJck.setHyf(new BigDecimal("0.00"));
                //其他费用	  qtfy            0.00
                oBillBJck.setQtfy(new BigDecimal("0.00"));
                //收支项目编码  costsubj        2001
                oBillBJck.setCostsubj("2001");
                //收支项目名称  name_costsubj   货款
                oBillBJck.setNameCostsubj("货款");
                //            mq_op           i
                oBillBJck.setMqOp("i");
                //            mq_st           0
                oBillBJck.setMqSt("0");
                //            mq_count	      1
                oBillBJck.setMqCount(1);
                //            pk_corp	      1022
                oBillBJck.setPkCorp("1022");
                //            dr	          0
                oBillBJck.setDr(0);
                oBillBJcks.add(oBillBJck);
            }
        }
        return oBillBJcks;
    }


}