package com.dcjet.cs.dec.dao;


import com.dcjet.cs.dec.model.BizIncomingGoodsList;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import tk.mybatis.mapper.common.Mapper;

/**
 * 进过管理-表体列表Mapper
 */
public interface BizIncomingGoodsListMapper extends Mapper<BizIncomingGoodsList>{

    /**
     * 查询获取数据
     * @param bizIncomingGoodsList
     * @return
     */
    List<BizIncomingGoodsList> getList(BizIncomingGoodsList bizIncomingGoodsList);

    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(@Param("list")List<String> sids);

    List<BizIncomingGoodsList> getListSumByInvoice(@Param("headId") String headId, @Param("tradeCode") String tradeCode);

    int batchUpdateInvoiceNo(@Param("idList") List<String> idList,
                             @Param("invoiceNo") String invoiceNo,
                             @Param("userNo")String userNo,
                             @Param("userName")String userName);
}