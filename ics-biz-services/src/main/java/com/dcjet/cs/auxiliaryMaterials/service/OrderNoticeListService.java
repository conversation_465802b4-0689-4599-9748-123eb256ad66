package com.dcjet.cs.auxiliaryMaterials.service;

import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeListDto;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeListParam;
import com.dcjet.cs.auxiliaryMaterials.dao.OrderNoticeListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.OrderNoticeListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderNoticeListService extends BaseService<OrderNoticeList> {
    @Resource
    private OrderNoticeListMapper orderNoticeListMapper;
    @Resource
    private OrderNoticeListDtoMapper orderNoticeListDtoMapper;

    @Override
    public Mapper<OrderNoticeList> getMapper() {
        return this.orderNoticeListMapper;
    }

    /**
     * 获取分页列表
     *
     * @param orderNoticeListParam 订货通知表体参数
     * @param pageParam            分页参数
     * @param userInfo             用户信息
     * @return 订货通知表体分页列表模型
     */
    public ResultObject<List<OrderNoticeListDto>> getListPaged(OrderNoticeListParam orderNoticeListParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        OrderNoticeList orderNoticeList = this.orderNoticeListDtoMapper.toPo(orderNoticeListParam);
        orderNoticeList.setTradeCode(userInfo.getCompany());
        Page<OrderNoticeList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> this.orderNoticeListMapper.getList(orderNoticeList));
        List<OrderNoticeListDto> listDtoList = page.getResult().stream()
                .map(onl -> this.orderNoticeListDtoMapper.toDto(onl))
                .collect(Collectors.toList());
        return ResultObject.createInstance(listDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 修改订货通知表体
     *
     * @param orderNoticeListParam 订货通知表体参数
     * @param userInfo             用户信息
     * @return 订货通知表体传输模型
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderNoticeListDto update(OrderNoticeListParam orderNoticeListParam, UserInfoToken<?> userInfo) {
        OrderNoticeList orderNoticeList = this.orderNoticeListMapper.selectByPrimaryKey(orderNoticeListParam.getSid());
        orderNoticeList.setQty(orderNoticeListParam.getQty());
        orderNoticeList.setTransportMode(orderNoticeListParam.getTransportMode());
        orderNoticeList.setPort(orderNoticeListParam.getPort());
        orderNoticeList.setUpdateTime(new Date());
        orderNoticeList.setUpdateUser(userInfo.getUserNo());
        orderNoticeList.setUpdateUserName(userInfo.getUserName());
        return this.orderNoticeListMapper.updateByPrimaryKey(orderNoticeList) > 0 ?
                this.orderNoticeListDtoMapper.toDto(orderNoticeList) : null;
    }

    /**
     * 根据主键列表删除
     *
     * @param sids     主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySids(List<String> sids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        log.info("user {} of company {} deleted records with sids {}", userInfo.getUserNo(), userInfo.getCompany(), sids);
        this.orderNoticeListMapper.deleteBySids(sids);
    }
}