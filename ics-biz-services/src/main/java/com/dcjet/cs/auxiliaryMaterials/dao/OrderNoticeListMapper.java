package com.dcjet.cs.auxiliaryMaterials.dao;

import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeList;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface OrderNoticeListMapper extends Mapper<OrderNoticeList> {
    /**
     * 获取列表
     *
     * @param orderNoticeList 订货通知表体
     * @return 订货通知表体列表
     */
    List<OrderNoticeList> getList(OrderNoticeList orderNoticeList);

    /**
     * 根据主键列表删除
     *
     * @param sids 主键列表
     * @return deleted rows count
     */
    int deleteBySids(@Param("sids") List<String> sids);

    /**
     * 根据表头主键列表删除
     *
     * @param headIds 表头主键列表
     * @return deleted rows count
     */
    int deleteByHeadIds(@Param("headIds") List<String> headIds);
}