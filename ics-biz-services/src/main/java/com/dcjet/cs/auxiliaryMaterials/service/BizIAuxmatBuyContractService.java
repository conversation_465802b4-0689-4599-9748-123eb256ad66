package com.dcjet.cs.auxiliaryMaterials.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatBuyContractMapper;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatBuyContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatBuyContractDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatBuyContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContract;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import com.dcjet.cs.auxiliaryMaterials.service.BizIAuxmatBuyContractListService;
import com.dcjet.cs.util.CommonEnum;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import com.xdo.common.exception.ErrorException;
import xdoi18n.XdoI18nUtil;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-28
 */
@Service
public class BizIAuxmatBuyContractService extends BaseService<BizIAuxmatBuyContract> {
    @Resource
    private BizIAuxmatBuyContractMapper bizIAuxmatBuyContractMapper;
    @Resource
    private BizIAuxmatBuyContractDtoMapper bizIAuxmatBuyContractDtoMapper;
    @Resource
    private BizIAuxmatBuyContractListMapper bizIAuxmatBuyContractListMapper;
    @Resource
    private BizIAuxmatBuyContractListDtoMapper bizIAuxmatBuyContractListDtoMapper;
    @Resource
    private BizIAuxmatBuyContractListService bizIAuxmatBuyContractListService;
    @Override
    public Mapper<BizIAuxmatBuyContract> getMapper() {
        return bizIAuxmatBuyContractMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIAuxmatBuyContractParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIAuxmatBuyContractDto>> getListPaged(BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, PageParam pageParam) {
        // 启用分页查询
        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractDtoMapper.toPo(bizIAuxmatBuyContractParam);
        Page<BizIAuxmatBuyContract> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIAuxmatBuyContractMapper.getList(bizIAuxmatBuyContract));
        List<BizIAuxmatBuyContractDto> bizIAuxmatBuyContractDtos = page.getResult().stream().map(head -> {
            BizIAuxmatBuyContractDto dto = bizIAuxmatBuyContractDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIAuxmatBuyContractDto>> paged = ResultObject.createInstance(bizIAuxmatBuyContractDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIAuxmatBuyContractParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatBuyContractDto insert(BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, UserInfoToken userInfo) {
        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractDtoMapper.toPo(bizIAuxmatBuyContractParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIAuxmatBuyContract.setId(sid);
        bizIAuxmatBuyContract.setCreateBy(userInfo.getUserNo());
        bizIAuxmatBuyContract.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizIAuxmatBuyContractMapper.insert(bizIAuxmatBuyContract);
        return  insertStatus > 0 ? bizIAuxmatBuyContractDtoMapper.toDto(bizIAuxmatBuyContract) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIAuxmatBuyContractParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatBuyContractDto update(BizIAuxmatBuyContractParam bizIAuxmatBuyContractParam, UserInfoToken userInfo) {
        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(bizIAuxmatBuyContractParam.getId());
        bizIAuxmatBuyContractDtoMapper.updatePo(bizIAuxmatBuyContractParam, bizIAuxmatBuyContract);
        bizIAuxmatBuyContract.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatBuyContract.setUpdateTime(new Date());
        // 更新数据
        int update = bizIAuxmatBuyContractMapper.updateByPrimaryKey(bizIAuxmatBuyContract);
        return update > 0 ? bizIAuxmatBuyContractDtoMapper.toDto(bizIAuxmatBuyContract) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizIAuxmatBuyContractMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIAuxmatBuyContractDto> selectAll(BizIAuxmatBuyContractParam exportParam, UserInfoToken userInfo) {
        BizIAuxmatBuyContract bizIAuxmatBuyContract = bizIAuxmatBuyContractDtoMapper.toPo(exportParam);
        // bizIAuxmatBuyContract.setTradeCode(userInfo.getCompany());
        List<BizIAuxmatBuyContractDto> bizIAuxmatBuyContractDtos = new ArrayList<>();
        List<BizIAuxmatBuyContract> bizIAuxmatBuyContracts = bizIAuxmatBuyContractMapper.getList(bizIAuxmatBuyContract);
        if (CollectionUtils.isNotEmpty(bizIAuxmatBuyContracts)) {
            bizIAuxmatBuyContractDtos = bizIAuxmatBuyContracts.stream().map(head -> {
                BizIAuxmatBuyContractDto dto = bizIAuxmatBuyContractDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIAuxmatBuyContractDtos;
    }

    /**
     * 确认数据状态接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject confirmStatus(String sid, UserInfoToken userInfo) {
        BizIAuxmatBuyContract contract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(sid);
        if (contract == null) {
            throw new ErrorException(400, "辅料购销合同数据不存在，请联系管理员");
        }

        // 假设状态字段为 status，1表示已确认
        if ("1".equals(contract.getStatus())) {
            throw new ErrorException(400, "该数据已经确认，无需重复操作");
        }

        contract.setStatus("1");
        contract.setUpdateBy(userInfo.getUserNo());
        contract.setUpdateTime(new Date());

        bizIAuxmatBuyContractMapper.updateByPrimaryKey(contract);

        return ResultObject.createInstance(true, "确认成功");
    }

    /**
     * 发送审批接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject sendApproval(String sid, UserInfoToken userInfo) {
        BizIAuxmatBuyContract contract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(sid);
        if (contract == null) {
            throw new ErrorException(400, "辅料购销合同数据不存在，请联系管理员");
        }

        if (!"1".equals(contract.getStatus())) {
            throw new ErrorException(400, "只有数据状态为确认的数据允许操作发送审批");
        }

        // 假设审批状态字段为 apprStatus，2表示审批中
        if ("2".equals(contract.getApprStatus())) {
            throw new ErrorException(400, "该数据已在审批中，无需重复操作");
        }

        contract.setApprStatus("2");
        contract.setUpdateBy(userInfo.getUserNo());
        contract.setUpdateTime(new Date());

        bizIAuxmatBuyContractMapper.updateByPrimaryKey(contract);

        return ResultObject.createInstance(true, "发送审批成功");
    }

    /**
     * 作废接口
     * @param sid 主键ID
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject invalidate(String sid, UserInfoToken userInfo) {
        BizIAuxmatBuyContract contract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(sid);
        if (contract == null) {
            throw new ErrorException(400, "辅料购销合同数据不存在，请联系管理员");
        }

        if ("2".equals(contract.getApprStatus())) {
            throw new ErrorException(400, "审批中的数据不允许作废");
        }

        if ("2".equals(contract.getStatus())) {
            throw new ErrorException(400, "该数据已经作废，无需重复操作");
        }

        contract.setStatus("2");
        contract.setUpdateBy(userInfo.getUserNo());
        contract.setUpdateTime(new Date());

        bizIAuxmatBuyContractMapper.updateByPrimaryKey(contract);

        return ResultObject.createInstance(true, "作废成功");
    }

    /**
     * 校验是否存在同一个合同号是否存在未作废的数据
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    public ResultObject checkContractIdNotCancel(BizIAuxmatBuyContractParam params, UserInfoToken userInfo) {
        if (StringUtils.isBlank(params.getContractNo())) {
            throw new ErrorException(400, "合同号不能为空");
        }

        // 查询同一合同号下未作废的数据数量
        BizIAuxmatBuyContract queryParam = new BizIAuxmatBuyContract();
        queryParam.setContractNo(params.getContractNo());
        queryParam.setTradeCode(userInfo.getCompany());

        List<BizIAuxmatBuyContract> contracts = bizIAuxmatBuyContractMapper.getList(queryParam);
        long notCancelledCount = contracts.stream()
                .filter(contract -> !"2".equals(contract.getStatus()))
                .count();

        if (notCancelledCount > 1) {
            return ResultObject.createInstance(false, "同一合同号存在多条未作废的数据，请检查", notCancelledCount);
        }

        return ResultObject.createInstance(true, "校验通过", notCancelledCount);
    }

    /**
     * 版本复制
     * @param params 请求参数
     * @param userInfo 用户信息
     * @return 返回结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject copyVersion(BizIAuxmatBuyContractParam params, UserInfoToken userInfo) {
        if (StringUtils.isBlank(params.getId())) {
            throw new ErrorException(400, "原数据ID不能为空");
        }

        BizIAuxmatBuyContract originalContract = bizIAuxmatBuyContractMapper.selectByPrimaryKey(params.getId());
        if (originalContract == null) {
            throw new ErrorException(400, "原数据不存在，请联系管理员");
        }

        // 创建新的合同记录
        BizIAuxmatBuyContract newContract = new BizIAuxmatBuyContract();
        // 复制原数据的字段
        newContract.setBusinessType(originalContract.getBusinessType());
        newContract.setContractNo(originalContract.getContractNo());
        newContract.setContractYear(originalContract.getContractYear());
        newContract.setBusinessDistinction(originalContract.getBusinessDistinction());
        newContract.setRemark(originalContract.getRemark());
        newContract.setTradeCode(originalContract.getTradeCode());

        // 设置新的ID和版本信息
        String newSid = UUID.randomUUID().toString();
        newContract.setId(newSid);
        newContract.setStatus("0"); // 新建状态
        newContract.setApprStatus("0"); // 未审批状态
        newContract.setCreateBy(userInfo.getUserNo());
        newContract.setCreateTime(new Date());

        // 生成新的版本号
        String newVersionNo = generateNewVersionNo(originalContract.getContractNo(), userInfo.getCompany());
        newContract.setVersionNo(newVersionNo);

        bizIAuxmatBuyContractMapper.insert(newContract);

        // 复制表体数据
        copyContractListData(params.getId(), newSid, userInfo);

        return ResultObject.createInstance(true, "版本复制成功", newSid);
    }

    /**
     * 生成新的版本号
     * @param contractNo 合同号
     * @param tradeCode 企业代码
     * @return 新版本号
     */
    private String generateNewVersionNo(String contractNo, String tradeCode) {
        BizIAuxmatBuyContract queryParam = new BizIAuxmatBuyContract();
        queryParam.setContractNo(contractNo);
        queryParam.setTradeCode(tradeCode);

        List<BizIAuxmatBuyContract> contracts = bizIAuxmatBuyContractMapper.getList(queryParam);

        int maxVersion = contracts.stream()
                .filter(contract -> contract.getVersionNo() != null)
                .mapToInt(contract -> {
                    try {
                        return Integer.parseInt(contract.getVersionNo());
                    } catch (NumberFormatException e) {
                        return 0;
                    }
                })
                .max()
                .orElse(0);

        return String.valueOf(maxVersion + 1);
    }

    /**
     * 复制表体数据
     * @param originalHeadId 原表头ID
     * @param newHeadId 新表头ID
     * @param userInfo 用户信息
     */
    private void copyContractListData(String originalHeadId, String newHeadId, UserInfoToken userInfo) {
        BizIAuxmatBuyContractList queryParam = new BizIAuxmatBuyContractList();
        queryParam.setHeadId(originalHeadId);

        List<BizIAuxmatBuyContractList> originalLists = bizIAuxmatBuyContractListMapper.getList(queryParam);

        for (BizIAuxmatBuyContractList originalList : originalLists) {
            BizIAuxmatBuyContractList newList = new BizIAuxmatBuyContractList();

            // 复制字段
            newList.setId(UUID.randomUUID().toString());
            newList.setHeadId(newHeadId);
            newList.setGName(originalList.getGName());
            newList.setGModel(originalList.getGModel());
            newList.setSpecifications(originalList.getSpecifications());
            newList.setGramWeight(originalList.getGramWeight());
            newList.setSupplier(originalList.getSupplier());
            newList.setQty(originalList.getQty());
            newList.setUnit(originalList.getUnit());
            newList.setUnitPrice(originalList.getUnitPrice());
            newList.setAmount(originalList.getAmount());
            newList.setCurr(originalList.getCurr());
            newList.setTradeCode(originalList.getTradeCode());
            newList.setCreateBy(userInfo.getUserNo());
            newList.setCreateTime(new Date());

            bizIAuxmatBuyContractListMapper.insert(newList);
        }
    }

    /**
     * 校验单行表体数据
     * @param detail 表体数据
     * @return 返回校验结果错误信息，如果为空则校验通过
     */
    public String validateSingleDetailData(BizIAuxmatBuyContractListParam detail) {
        try {
            // 校验商品名称（必填字段）
            if (StringUtils.isBlank(detail.getGName())) {
                return "商品名称不能为空";
            }

            // 校验商品名称长度（字符型60）
            if (detail.getGName().length() > 60) {
                return "商品名称【" + detail.getGName() + "】长度不能超过60个字符";
            }

            // 校验产品型号长度（字符型100，非必填）
            if (StringUtils.isNotBlank(detail.getGModel()) && detail.getGModel().length() > 100) {
                return "商品名称【" + detail.getGName() + "】的产品型号长度不能超过100个字符";
            }

            // 校验规格长度（字符型100，非必填）
            if (StringUtils.isNotBlank(detail.getSpecifications()) && detail.getSpecifications().length() > 100) {
                return "商品名称【" + detail.getGName() + "】的规格长度不能超过100个字符";
            }

            // 校验克重（数值型19,4，非必填）
            if (detail.getGramWeight() != null) {
                if (detail.getGramWeight().compareTo(java.math.BigDecimal.ZERO) < 0) {
                    return "商品名称【" + detail.getGName() + "】的克重不能为负数";
                }
                // 检查精度：整数位最大19位，小数位最大4位
                String gramWeightStr = detail.getGramWeight().toPlainString();
                if (gramWeightStr.contains(".")) {
                    String[] parts = gramWeightStr.split("\\.");
                    if (parts[0].length() > 19) {
                        return "商品名称【" + detail.getGName() + "】的克重整数位不能超过19位";
                    }
                    if (parts[1].length() > 4) {
                        return "商品名称【" + detail.getGName() + "】的克重小数位不能超过4位";
                    }
                } else {
                    if (gramWeightStr.length() > 19) {
                        return "商品名称【" + detail.getGName() + "】的克重整数位不能超过19位";
                    }
                }
            }

            // 校验供应商长度（字符型200，非必填）
            if (StringUtils.isNotBlank(detail.getSupplier()) && detail.getSupplier().length() > 200) {
                return "商品名称【" + detail.getGName() + "】的供应商长度不能超过200个字符";
            }

            // 校验数量（数值型19,6，必填）
            if (detail.getQty() == null) {
                return "商品名称【" + detail.getGName() + "】的数量不能为空";
            }
            if (detail.getQty().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return "商品名称【" + detail.getGName() + "】的数量必须大于0";
            }
            // 检查数量精度：整数位最大19位，小数位最大6位
            String qtyStr = detail.getQty().toPlainString();
            if (qtyStr.contains(".")) {
                String[] parts = qtyStr.split("\\.");
                if (parts[0].length() > 19) {
                    return "商品名称【" + detail.getGName() + "】的数量整数位不能超过19位";
                }
                if (parts[1].length() > 6) {
                    return "商品名称【" + detail.getGName() + "】的数量小数位不能超过6位";
                }
            } else {
                if (qtyStr.length() > 19) {
                    return "商品名称【" + detail.getGName() + "】的数量整数位不能超过19位";
                }
            }

            // 校验单位长度（字符型30，非必填）
            if (StringUtils.isNotBlank(detail.getUnit()) && detail.getUnit().length() > 30) {
                return "商品名称【" + detail.getGName() + "】的单位长度不能超过30个字符";
            }

            // 校验单价（数值型19,8，必填）
            if (detail.getUnitPrice() == null) {
                return "商品名称【" + detail.getGName() + "】的单价不能为空";
            }
            if (detail.getUnitPrice().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return "商品名称【" + detail.getGName() + "】的单价必须大于0";
            }
            // 检查单价精度：整数位最大19位，小数位最大8位
            String unitPriceStr = detail.getUnitPrice().toPlainString();
            if (unitPriceStr.contains(".")) {
                String[] parts = unitPriceStr.split("\\.");
                if (parts[0].length() > 19) {
                    return "商品名称【" + detail.getGName() + "】的单价整数位不能超过19位";
                }
                if (parts[1].length() > 8) {
                    return "商品名称【" + detail.getGName() + "】的单价小数位不能超过8位";
                }
            } else {
                if (unitPriceStr.length() > 19) {
                    return "商品名称【" + detail.getGName() + "】的单价整数位不能超过19位";
                }
            }

            // 校验金额（数值型19,4，必填，系统计算=数量*单价）
            if (detail.getAmount() == null) {
                return "商品名称【" + detail.getGName() + "】的金额不能为空";
            }
            if (detail.getAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return "商品名称【" + detail.getGName() + "】的金额必须大于0";
            }
            // 检查金额精度：整数位最大19位，小数位最大4位
            String amountStr = detail.getAmount().toPlainString();
            if (amountStr.contains(".")) {
                String[] parts = amountStr.split("\\.");
                if (parts[0].length() > 19) {
                    return "商品名称【" + detail.getGName() + "】的金额整数位不能超过19位";
                }
                if (parts[1].length() > 4) {
                    return "商品名称【" + detail.getGName() + "】的金额小数位不能超过4位";
                }
            } else {
                if (amountStr.length() > 19) {
                    return "商品名称【" + detail.getGName() + "】的金额整数位不能超过19位";
                }
            }

            // 校验币种（字符型10，必填）
            if (StringUtils.isBlank(detail.getCurr())) {
                return "商品名称【" + detail.getGName() + "】的币种不能为空";
            }
            if (detail.getCurr().length() > 10) {
                return "商品名称【" + detail.getGName() + "】的币种长度不能超过10个字符";
            }

            // 校验金额计算是否正确（数量 × 单价 = 金额）
            java.math.BigDecimal calculatedAmount = detail.getQty().multiply(detail.getUnitPrice());
            // 由于金额小数位最大4位，所以允许的误差为0.0001
            if (calculatedAmount.subtract(detail.getAmount()).abs().compareTo(new java.math.BigDecimal("0.0001")) > 0) {
                return "商品名称【" + detail.getGName() + "】的金额计算不正确，应为：" + calculatedAmount.setScale(4, java.math.RoundingMode.HALF_UP);
            }

            return ""; // 校验通过
        } catch (Exception e) {
            String productName = detail != null && StringUtils.isNotBlank(detail.getGName()) ? detail.getGName() : "未知商品";
            return "商品名称【" + productName + "】校验时发生异常：" + e.getMessage();
        }
    }

}
