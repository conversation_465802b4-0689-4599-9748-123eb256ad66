package com.dcjet.cs.auxiliaryMaterials.service;

import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeHeadDto;
import com.dcjet.cs.dto.auxiliaryMaterials.OrderNoticeHeadParam;
import com.dcjet.cs.auxiliaryMaterials.dao.OrderNoticeHeadMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.OrderNoticeHeadDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderNoticeHeadService extends BaseService<OrderNoticeHead> {
    @Resource
    private OrderNoticeHeadMapper orderNoticeHeadMapper;
    @Resource
    private OrderNoticeHeadDtoMapper orderNoticeHeadDtoMapper;

    @Override
    public Mapper<OrderNoticeHead> getMapper() {
        return this.orderNoticeHeadMapper;
    }

    /**
     * 获取分页列表
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param pageParam            分页参数
     * @param userInfo             用户信息
     * @return 订货通知表头分页列表模型
     */
    public ResultObject<List<OrderNoticeHeadDto>> getListPaged(OrderNoticeHeadParam orderNoticeHeadParam
            , PageParam pageParam, UserInfoToken<?> userInfo) {
        OrderNoticeHead orderNoticeHead = this.orderNoticeHeadDtoMapper.toPo(orderNoticeHeadParam);
        orderNoticeHead.setTradeCode(userInfo.getCompany());
        Page<OrderNoticeHead> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> this.orderNoticeHeadMapper.getList(orderNoticeHead));
        List<OrderNoticeHeadDto> headDtoList = page.getResult().stream()
                .map(onh -> this.orderNoticeHeadDtoMapper.toDto(onh))
                .collect(Collectors.toList());
        return ResultObject.createInstance(headDtoList, (int) page.getTotal(), page.getPageNum());
    }

    /**
     * 新增订货通知表头
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 订货通知表头传输模型
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderNoticeHeadDto insert(OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        OrderNoticeHead orderNoticeHead = this.orderNoticeHeadDtoMapper.toPo(orderNoticeHeadParam);
        orderNoticeHead.setSid(UUID.randomUUID().toString());
        orderNoticeHead.setTradeCode(userInfo.getCompany());
        orderNoticeHead.setInsertUser(userInfo.getUserNo());
        orderNoticeHead.setInsertUserName(userInfo.getUserName());
        orderNoticeHead.setInsertTime(new Date());
        return this.orderNoticeHeadMapper.insert(orderNoticeHead) > 0 ?
                this.orderNoticeHeadDtoMapper.toDto(orderNoticeHead) : null;
    }

    /**
     * 修改订货通知表头
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 订货通知表头传输模型
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderNoticeHeadDto update(OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        OrderNoticeHead orderNoticeHead = this.orderNoticeHeadMapper.selectByPrimaryKey(orderNoticeHeadParam.getSid());
        orderNoticeHead.setUpdateTime(new Date());
        orderNoticeHead.setUpdateUser(userInfo.getUserNo());
        orderNoticeHead.setUpdateUserName(userInfo.getUserName());
        return this.orderNoticeHeadMapper.updateByPrimaryKey(orderNoticeHead) > 0 ?
                this.orderNoticeHeadDtoMapper.toDto(orderNoticeHead) : null;
    }

    /**
     * 根据主键列表删除
     *
     * @param sids     主键列表
     * @param userInfo 用户信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteBySids(List<String> sids, UserInfoToken<?> userInfo) {
        if (CollectionUtils.isEmpty(sids)) {
            return;
        }
        log.info("user {} of company {} deleted records with {}", userInfo.getUserNo(), userInfo.getCompany(), sids);
        this.orderNoticeHeadMapper.deleteBySids(sids);
    }

    /**
     * 获取所有列表
     *
     * @param orderNoticeHeadParam 订货通知表头参数
     * @param userInfo             用户信息
     * @return 订货通知表头列表
     */
    public List<OrderNoticeHeadDto> getAllList(OrderNoticeHeadParam orderNoticeHeadParam, UserInfoToken<?> userInfo) {
        OrderNoticeHead orderNoticeHead = this.orderNoticeHeadDtoMapper.toPo(orderNoticeHeadParam);
        orderNoticeHead.setTradeCode(userInfo.getCompany());
        List<OrderNoticeHead> orderNoticeHeadList = this.orderNoticeHeadMapper.getList(orderNoticeHead);
        if (CollectionUtils.isEmpty(orderNoticeHeadList)) {
            return Collections.emptyList();
        }
        List<OrderNoticeHeadDto> dtoList = orderNoticeHeadList.stream()
                .map(onh -> this.orderNoticeHeadDtoMapper.toDto(onh))
                .collect(Collectors.toList());
        dtoList.forEach(dto -> {

        });
        return dtoList;
    }
}