package com.dcjet.cs.auxiliaryMaterials.dao;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import tk.mybatis.mapper.common.Mapper;
import java.util.List;
/**
* generated by Generate 神码
* BizIAuxmatBuyContractList
* <AUTHOR>
* @date: 2025-5-29
*/
public interface BizIAuxmatBuyContractListMapper extends Mapper<BizIAuxmatBuyContractList> {
    /**
     * 查询获取数据
     * @param bizIAuxmatBuyContractList
     * @return
     */
    List<BizIAuxmatBuyContractList> getList(BizIAuxmatBuyContractList bizIAuxmatBuyContractList);
    /**
     * 批量删除
     * @param sids
     * @return 返回执行成功行数，0为执行失败
     */
    int deleteBySids(List<String> sids);
}
