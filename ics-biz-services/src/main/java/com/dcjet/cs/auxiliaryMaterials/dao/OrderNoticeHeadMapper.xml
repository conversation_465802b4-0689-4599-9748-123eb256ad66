<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.OrderNoticeHeadMapper">
    <resultMap id="orderNoticeHeadResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR"/>
        <result column="BUSINESS_TYPE" property="businessType" jdbcType="VARCHAR"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_DATE" property="orderDate" jdbcType="TIMESTAMP"/>
        <result column="CUSTOMER" property="customer" jdbcType="VARCHAR"/>
        <result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR"/>
        <result column="VERSION_NO" property="versionNo" jdbcType="VARCHAR"/>
        <result column="CREATE_USER" property="createUser" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="DATA_STATUS" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="PURCHASE_SALES_CONTRACT_NO" property="purchaseSalesContractNo" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="APPR_STATUS" property="apprStatus" jdbcType="VARCHAR"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        SID,
        PARENT_ID,
        BUSINESS_TYPE,
        ORDER_NO,
        ORDER_DATE,
        CUSTOMER,
        CUSTOMER_NAME,
        VERSION_NO,
        CREATE_USER,
        CREATE_TIME,
        DATA_STATUS,
        PURCHASE_SALES_CONTRACT_NO,
        NOTE,
        CONFIRM_TIME,
        APPR_STATUS,
        TRADE_CODE,
        INSERT_USER,
        INSERT_TIME,
        UPDATE_USER,
        UPDATE_TIME,
        INSERT_USER_NAME,
        UPDATE_USER_NAME,
        EXTEND1,
        EXTEND2,
        EXTEND3,
        EXTEND4,
        EXTEND5,
        EXTEND6,
        EXTEND7,
        EXTEND8,
        EXTEND9,
        EXTEND10
    </sql>

    <sql id="condition">
        <if test="true">
            TRADE_CODE = #{tradeCode}
        </if>
        <if test="dataStatus != null and dataStatus != ''">
            and DATA_STATUS = #{dataStatus}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and ORDER_NO like concat('%', #{orderNo}, '%')
        </if>
        <if test="customer != null and customer != ''">
            and CUSTOMER = #{customer}
        </if>
    </sql>

    <select id="getList" resultMap="orderNoticeHeadResultMap" parameterType="com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead">
        select
        <include refid="columns"/>
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD
        <where>
            <include refid="condition"/>
        </where>
        order by INSERT_TIME desc
    </select>

    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD where SID in
        <foreach collection="sids" item="sid" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>
</mapper>