package com.dcjet.cs.auxiliaryMaterials.dao;

import com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeHead;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface OrderNoticeHeadMapper extends Mapper<OrderNoticeHead> {
    /**
     * 获取列表
     *
     * @param orderNoticeHead 订货通知表头
     * @return 订货通知表头列表
     */
    List<OrderNoticeHead> getList(OrderNoticeHead orderNoticeHead);

    /**
     * 根据主键列表删除
     *
     * @param sids 主键列表
     * @return deleted rows count
     */
    int deleteBySids(@Param("sids") List<String> sids);
}