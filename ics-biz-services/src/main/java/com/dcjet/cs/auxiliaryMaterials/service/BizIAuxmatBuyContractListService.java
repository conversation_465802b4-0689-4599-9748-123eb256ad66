package com.dcjet.cs.auxiliaryMaterials.service;
import com.xdo.domain.PageParam;
import com.dcjet.cs.dto.auxiliaryMaterials.*;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.ResultObject;
import com.xdo.common.base.service.BaseService;
import com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatBuyContractListMapper;
import com.dcjet.cs.auxiliaryMaterials.mapper.BizIAuxmatBuyContractListDtoMapper;
import com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import jdk.nashorn.internal.runtime.options.Option;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import javax.annotation.Resource;
import java.beans.Transient;
import java.util.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2025-5-29
 */
@Service
public class BizIAuxmatBuyContractListService extends BaseService<BizIAuxmatBuyContractList> {
    @Resource
    private BizIAuxmatBuyContractListMapper bizIAuxmatBuyContractListMapper;
    @Resource
    private BizIAuxmatBuyContractListDtoMapper bizIAuxmatBuyContractListDtoMapper;
    @Override
    public Mapper<BizIAuxmatBuyContractList> getMapper() {
        return bizIAuxmatBuyContractListMapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param bizIAuxmatBuyContractListParam
     * @param pageParam
     * @return
     */
    public ResultObject<List<BizIAuxmatBuyContractListDto>> getListPaged(BizIAuxmatBuyContractListParam bizIAuxmatBuyContractListParam, PageParam pageParam) {
        // 启用分页查询
        BizIAuxmatBuyContractList bizIAuxmatBuyContractList = bizIAuxmatBuyContractListDtoMapper.toPo(bizIAuxmatBuyContractListParam);
        Page<BizIAuxmatBuyContractList> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> bizIAuxmatBuyContractListMapper.getList(bizIAuxmatBuyContractList));
        List<BizIAuxmatBuyContractListDto> bizIAuxmatBuyContractListDtos = page.getResult().stream().map(head -> {
            BizIAuxmatBuyContractListDto dto = bizIAuxmatBuyContractListDtoMapper.toDto(head);
            return dto;
        }).collect(Collectors.toList());
		ResultObject<List<BizIAuxmatBuyContractListDto>> paged = ResultObject.createInstance(bizIAuxmatBuyContractListDtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param bizIAuxmatBuyContractListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatBuyContractListDto insert(BizIAuxmatBuyContractListParam bizIAuxmatBuyContractListParam, UserInfoToken userInfo) {
        BizIAuxmatBuyContractList bizIAuxmatBuyContractList = bizIAuxmatBuyContractListDtoMapper.toPo(bizIAuxmatBuyContractListParam);
        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        bizIAuxmatBuyContractList.setId(sid);
        bizIAuxmatBuyContractList.setCreateBy(userInfo.getUserNo());
        bizIAuxmatBuyContractList.setCreateTime(new Date());
        // 新增数据
        int insertStatus = bizIAuxmatBuyContractListMapper.insert(bizIAuxmatBuyContractList);
        return  insertStatus > 0 ? bizIAuxmatBuyContractListDtoMapper.toDto(bizIAuxmatBuyContractList) : null;
    }
    /**
     * 功能描述:修改
     *
     * @param bizIAuxmatBuyContractListParam
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BizIAuxmatBuyContractListDto update(BizIAuxmatBuyContractListParam bizIAuxmatBuyContractListParam, UserInfoToken userInfo) {
        BizIAuxmatBuyContractList bizIAuxmatBuyContractList = bizIAuxmatBuyContractListMapper.selectByPrimaryKey(bizIAuxmatBuyContractListParam.getSid());
        bizIAuxmatBuyContractListDtoMapper.updatePo(bizIAuxmatBuyContractListParam, bizIAuxmatBuyContractList);
        bizIAuxmatBuyContractList.setUpdateBy(userInfo.getUserNo());
        bizIAuxmatBuyContractList.setUpdateTime(new Date());
        // 更新数据
        int update = bizIAuxmatBuyContractListMapper.updateByPrimaryKey(bizIAuxmatBuyContractList);
        return update > 0 ? bizIAuxmatBuyContractListDtoMapper.toDto(bizIAuxmatBuyContractList) : null;
    }
    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids, UserInfoToken userInfo) {
		bizIAuxmatBuyContractListMapper.deleteBySids(sids);
    }
    /**
     * 功能描述:查询所有数据
     *
     * @param exportParam
     * @param userInfo
     * @return
     */
    public List<BizIAuxmatBuyContractListDto> selectAll(BizIAuxmatBuyContractListParam exportParam, UserInfoToken userInfo) {
        BizIAuxmatBuyContractList bizIAuxmatBuyContractList = bizIAuxmatBuyContractListDtoMapper.toPo(exportParam);
        // bizIAuxmatBuyContractList.setTradeCode(userInfo.getCompany());
        List<BizIAuxmatBuyContractListDto> bizIAuxmatBuyContractListDtos = new ArrayList<>();
        List<BizIAuxmatBuyContractList> bizIAuxmatBuyContractLists = bizIAuxmatBuyContractListMapper.getList(bizIAuxmatBuyContractList);
        if (CollectionUtils.isNotEmpty(bizIAuxmatBuyContractLists)) {
            bizIAuxmatBuyContractListDtos = bizIAuxmatBuyContractLists.stream().map(head -> {
                BizIAuxmatBuyContractListDto dto = bizIAuxmatBuyContractListDtoMapper.toDto(head);
                return dto;
            }).collect(Collectors.toList());
        }
        return bizIAuxmatBuyContractListDtos;
    }
}
