<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.BizIAuxmatBuyContractListMapper">
    <resultMap id="bizIAuxmatBuyContractListResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList">
		<id column="id" property="id" jdbcType="VARCHAR" />
		<result column="head_id" property="headId" jdbcType="VARCHAR" />
		<result column="g_name" property="gName" jdbcType="VARCHAR" />
		<result column="g_model" property="gModel" jdbcType="VARCHAR" />
		<result column="specifications" property="specifications" jdbcType="VARCHAR" />
		<result column="gram_weight" property="gramWeight" jdbcType="NUMERIC" />
		<result column="supplier" property="supplier" jdbcType="VARCHAR" />
		<result column="qty" property="qty" jdbcType="NUMERIC" />
		<result column="unit" property="unit" jdbcType="VARCHAR" />
		<result column="unit_price" property="unitPrice" jdbcType="NUMERIC" />
		<result column="amount" property="amount" jdbcType="NUMERIC" />
		<result column="curr" property="curr" jdbcType="VARCHAR" />
		<result column="create_by" property="createBy" jdbcType="VARCHAR" />
		<result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
		<result column="update_by" property="updateBy" jdbcType="VARCHAR" />
		<result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
		<result column="sys_org_code" property="sysOrgCode" jdbcType="VARCHAR" />
		<result column="trade_code" property="tradeCode" jdbcType="VARCHAR" />
		<result column="EXTEND1" property="extend1" jdbcType="VARCHAR" />
		<result column="EXTEND2" property="extend2" jdbcType="VARCHAR" />
		<result column="EXTEND3" property="extend3" jdbcType="VARCHAR" />
		<result column="EXTEND4" property="extend4" jdbcType="VARCHAR" />
		<result column="EXTEND5" property="extend5" jdbcType="VARCHAR" />
		<result column="EXTEND6" property="extend6" jdbcType="VARCHAR" />
		<result column="EXTEND7" property="extend7" jdbcType="VARCHAR" />
		<result column="EXTEND8" property="extend8" jdbcType="VARCHAR" />
		<result column="EXTEND9" property="extend9" jdbcType="VARCHAR" />
		<result column="EXTEND10" property="extend10" jdbcType="VARCHAR" />
	</resultMap>
	<sql id="Base_Column_List" >
     id
     ,head_id
     ,g_name
     ,g_model
     ,specifications
     ,gram_weight
     ,supplier
     ,qty
     ,unit
     ,unit_price
     ,amount
     ,curr
     ,create_by
     ,create_time
     ,update_by
     ,update_time
     ,sys_org_code
     ,trade_code
     ,EXTEND1
     ,EXTEND2
     ,EXTEND3
     ,EXTEND4
     ,EXTEND5
     ,EXTEND6
     ,EXTEND7
     ,EXTEND8
     ,EXTEND9
     ,EXTEND10
    </sql>
    <sql id="condition">
    </sql>
    <!-- 列表查询 and 条件 begin-->
    <select id="getList" resultMap="bizIAuxmatBuyContractListResultMap" parameterType="com.dcjet.cs.auxiliaryMaterials.model.BizIAuxmatBuyContractList">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        t_biz_i_auxmat_buy_contract_list t
        <where>
            <include refid="condition"></include>
        </where>
    </select>
    <delete id="deleteBySids" parameterType="java.util.List">
        delete from t_biz_i_auxmat_buy_contract_list t where t.SID in
        <foreach collection="list"  item="item" open="(" separator="," close=")"  >
            #{item}
        </foreach>
    </delete>
</mapper>
