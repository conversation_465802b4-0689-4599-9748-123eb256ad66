<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dcjet.cs.auxiliaryMaterials.dao.OrderNoticeListMapper">
    <resultMap id="orderNoticeListResultMap" type="com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeList">
        <id column="SID" property="sid" jdbcType="VARCHAR"/>
        <result column="HEAD_ID" property="headId" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="PRODUCT_MODEL" property="productModel" jdbcType="VARCHAR"/>
        <result column="SPECIFICATION" property="specification" jdbcType="VARCHAR"/>
        <result column="WEIGHT" property="weight" jdbcType="NUMERIC"/>
        <result column="SUPPLIER" property="supplier" jdbcType="VARCHAR"/>
        <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR"/>
        <result column="TRANSPORT_MODE" property="transportMode" jdbcType="VARCHAR"/>
        <result column="PORT" property="port" jdbcType="VARCHAR"/>
        <result column="QTY" property="qty" jdbcType="NUMERIC"/>
        <result column="UNIT" property="unit" jdbcType="VARCHAR"/>
        <result column="REQUEST_DELIVERY_DATE" property="requestDeliveryDate" jdbcType="TIMESTAMP"/>
        <result column="DATA_STATUS" property="dataStatus" jdbcType="VARCHAR"/>
        <result column="PURCHASE_SALES_CONTRACT_NO" property="purchaseSalesContractNo" jdbcType="VARCHAR"/>
        <result column="NOTE" property="note" jdbcType="VARCHAR"/>
        <result column="CONFIRM_TIME" property="confirmTime" jdbcType="TIMESTAMP"/>
        <result column="TRADE_CODE" property="tradeCode" jdbcType="VARCHAR"/>
        <result column="INSERT_USER" property="insertUser" jdbcType="VARCHAR"/>
        <result column="INSERT_TIME" property="insertTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_USER" property="updateUser" jdbcType="VARCHAR"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="INSERT_USER_NAME" property="insertUserName" jdbcType="VARCHAR"/>
        <result column="UPDATE_USER_NAME" property="updateUserName" jdbcType="VARCHAR"/>
        <result column="EXTEND1" property="extend1" jdbcType="VARCHAR"/>
        <result column="EXTEND2" property="extend2" jdbcType="VARCHAR"/>
        <result column="EXTEND3" property="extend3" jdbcType="VARCHAR"/>
        <result column="EXTEND4" property="extend4" jdbcType="VARCHAR"/>
        <result column="EXTEND5" property="extend5" jdbcType="VARCHAR"/>
        <result column="EXTEND6" property="extend6" jdbcType="VARCHAR"/>
        <result column="EXTEND7" property="extend7" jdbcType="VARCHAR"/>
        <result column="EXTEND8" property="extend8" jdbcType="VARCHAR"/>
        <result column="EXTEND9" property="extend9" jdbcType="VARCHAR"/>
        <result column="EXTEND10" property="extend10" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="columns">
        SID,
        HEAD_ID,
        PRODUCT_NAME,
        PRODUCT_MODEL,
        SPECIFICATION,
        WEIGHT,
        SUPPLIER,
        SUPPLIER_NAME,
        TRANSPORT_MODE,
        PORT,
        QTY,
        UNIT,
        REQUEST_DELIVERY_DATE,
        DATA_STATUS,
        PURCHASE_SALES_CONTRACT_NO,
        NOTE,
        CONFIRM_TIME,
        TRADE_CODE,
        INSERT_USER,
        INSERT_TIME,
        UPDATE_USER,
        UPDATE_TIME,
        INSERT_USER_NAME,
        UPDATE_USER_NAME,
        EXTEND1,
        EXTEND2,
        EXTEND3,
        EXTEND4,
        EXTEND5,
        EXTEND6,
        EXTEND7,
        EXTEND8,
        EXTEND9,
        EXTEND10
    </sql>

    <sql id="condition">
        <if test="true">
            TRADE_CODE = #{tradeCode}
        </if>
        <if test="true">
            and HEAD_ID = #{headId}
        </if>
    </sql>

    <select id="getList" resultMap="orderNoticeListResultMap"
            parameterType="com.dcjet.cs.auxiliaryMaterials.model.OrderNoticeList">
        select
        <include refid="columns"/>
        from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST
        <where>
            <include refid="condition"/>
        </where>
        order by INSERT_TIME desc
    </select>

    <delete id="deleteBySids" parameterType="java.util.List">
        delete from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST where SID in
        <foreach collection="sids" item="sid" open="(" separator="," close=")">
            #{sid}
        </foreach>
    </delete>

    <delete id="deleteByHeadIds" parameterType="java.util.List">
        delete from T_BIZ_I_AUXMAT_ORDER_NOTICE_LIST where HEAD_ID in
        <foreach collection="headIds" item="headId" open="(" separator="," close=")">
            #{headId}
        </foreach>
    </delete>
</mapper>