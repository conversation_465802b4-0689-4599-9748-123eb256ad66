package com.dcjet.cs.auxiliaryMaterials.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@Setter
@Getter
@Table(name = "T_BIZ_I_AUXMAT_ORDER_NOTICE_HEAD")
public class OrderNoticeHead implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "SID")
    private String sid;

    /**
     * 父ID
     */
    @Column(name = "PARENT_ID")
    private String parentId;

    /**
     * 业务类型
     */
    @Column(name = "BUSINESS_TYPE")
    private String businessType;

    /**
     * 订货编号
     */
    @Column(name = "ORDER_NO")
    private String orderNo;

    /**
     * 订货日期
     */
    @Column(name = "ORDER_DATE")
    private Date orderDate;

    /**
     * 客户
     */
    @Column(name = "CUSTOMER")
    private String customer;

    /**
     * 客户名称
     */
    @Column(name = "CUSTOMER_NAME")
    private String customerName;

    /**
     * 版本号
     */
    @Column(name = "VERSION_NO")
    private String versionNo;

    /**
     * 制单人
     */
    @Column(name = "CREATE_USER")
    private String createUser;

    /**
     * 制单时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 单据状态 0编制 1确认 2作废
     */
    @Column(name = "DATA_STATUS")
    private String dataStatus;

    /**
     * 购销合同号
     */
    @Column(name = "PURCHASE_SALES_CONTRACT_NO")
    private String purchaseSalesContractNo;

    /**
     * 备注
     */
    @Column(name = "NOTE")
    private String note;

    /**
     * 确认时间
     */
    @Column(name = "CONFIRM_TIME")
    private Date confirmTime;

    /**
     * 审批状态 0不涉及审批 1未审批 2审批中 3审批通过 4审批退回
     */
    @Column(name = "APPR_STATUS")
    private String apprStatus;

    /**
     * 企业编码
     */
    @Column(name = "TRADE_CODE")
    private String tradeCode;

    /**
     * 新增用户
     */
    @Column(name = "INSERT_USER")
    private String insertUser;

    /**
     * 新增时间
     */
    @Column(name = "INSERT_TIME")
    private Date insertTime;

    /**
     * 修改用户
     */
    @Column(name = "UPDATE_USER")
    private String updateUser;

    /**
     * 修改时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 新增用户名称
     */
    @Column(name = "INSERT_USER_NAME")
    private String insertUserName;

    /**
     * 修改用户名称
     */
    @Column(name = "UPDATE_USER_NAME")
    private String updateUserName;

    /**
     * 扩展字段1
     */
    @Column(name = "EXTEND1")
    private String extend1;

    /**
     * 扩展字段2
     */
    @Column(name = "EXTEND2")
    private String extend2;
    /**
     * 扩展字段3
     */
    @Column(name = "EXTEND3")
    private String extend3;

    /**
     * 扩展字段4
     */
    @Column(name = "EXTEND4")
    private String extend4;

    /**
     * 扩展字段5
     */
    @Column(name = "EXTEND5")
    private String extend5;

    /**
     * 扩展字段6
     */
    @Column(name = "EXTEND6")
    private String extend6;

    /**
     * 扩展字段7
     */
    @Column(name = "EXTEND7")
    private String extend7;

    /**
     * 扩展字段8
     */
    @Column(name = "EXTEND8")
    private String extend8;

    /**
     * 扩展字段9
     */
    @Column(name = "EXTEND9")
    private String extend9;

    /**
     * 扩展字段10
     */
    @Column(name = "EXTEND10")
    private String extend10;
}